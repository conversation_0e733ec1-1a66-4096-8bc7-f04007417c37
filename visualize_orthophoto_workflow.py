#!/usr/bin/env python3
"""
正射影像生成工作流程可视化
展示从原始影像到正射影像的完整流程
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, Polygon
import os
from matplotlib.gridspec import GridSpec

def setup_chinese_font():
    """设置中文字体"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

def create_workflow_visualization():
    """创建工作流程可视化"""
    chinese_ok = setup_chinese_font()
    
    # 检查必要文件
    required_files = [
        'orthophoto_labels.png',
        'orthophoto_voting_stats.json',
        'orthophoto_geo_info.json'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"缺少文件: {missing_files}")
        return
    
    # 读取数据
    label_orthophoto = cv2.imread('orthophoto_labels.png', cv2.IMREAD_GRAYSCALE)
    label_orthophoto = (label_orthophoto > 0).astype(np.uint8)
    
    with open('orthophoto_voting_stats.json', 'r', encoding='utf-8') as f:
        voting_stats = json.load(f)
    
    with open('orthophoto_geo_info.json', 'r', encoding='utf-8') as f:
        geo_info = json.load(f)
    
    # 创建综合可视化
    fig = plt.figure(figsize=(20, 16))
    gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)
    
    # 1. 工作流程示意图
    ax1 = fig.add_subplot(gs[0, :])
    create_workflow_diagram(ax1, chinese_ok)
    
    # 2. 原始图像示例
    ax2 = fig.add_subplot(gs[1, 0])
    show_original_image_sample(ax2, chinese_ok)
    
    # 3. 标注示例
    ax3 = fig.add_subplot(gs[1, 1])
    show_annotation_sample(ax3, chinese_ok)
    
    # 4. 正射影像标签结果
    ax4 = fig.add_subplot(gs[1, 2])
    im4 = ax4.imshow(label_orthophoto, cmap='RdYlBu_r', interpolation='nearest')
    if chinese_ok:
        ax4.set_title('正射影像标签结果', fontsize=12, fontweight='bold')
        ax4.set_xlabel('X (像素)')
        ax4.set_ylabel('Y (像素)')
    else:
        ax4.set_title('Orthophoto Label Result', fontsize=12, fontweight='bold')
        ax4.set_xlabel('X (pixels)')
        ax4.set_ylabel('Y (pixels)')
    
    plt.colorbar(im4, ax=ax4, shrink=0.8)
    
    # 5. 处理统计
    ax5 = fig.add_subplot(gs[2, 0])
    create_processing_stats_chart(ax5, voting_stats, chinese_ok)
    
    # 6. 标注覆盖率
    ax6 = fig.add_subplot(gs[2, 1])
    create_coverage_pie_chart(ax6, voting_stats, chinese_ok)
    
    # 7. 地理信息表格
    ax7 = fig.add_subplot(gs[2, 2])
    create_geo_info_table(ax7, geo_info, voting_stats, chinese_ok)
    
    # 8. 技术流程说明
    ax8 = fig.add_subplot(gs[3, :])
    create_technical_explanation(ax8, chinese_ok)
    
    # 设置总标题
    if chinese_ok:
        fig.suptitle('DJI航拍图像正射影像生成和标注映射工作流程', 
                    fontsize=16, fontweight='bold', y=0.98)
    else:
        fig.suptitle('DJI Aerial Image Orthophoto Generation and Annotation Mapping Workflow', 
                    fontsize=16, fontweight='bold', y=0.98)
    
    plt.savefig('orthophoto_workflow_visualization.png', dpi=300, bbox_inches='tight')
    print("工作流程可视化已保存: orthophoto_workflow_visualization.png")
    plt.close()

def create_workflow_diagram(ax, chinese_ok):
    """创建工作流程示意图"""
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 3)
    ax.axis('off')
    
    if chinese_ok:
        # 绘制流程框
        boxes = [
            (1, 2, "原始影像\n(34张DJI图像)"),
            (3, 2.5, "元数据提取\n(GPS+相机参数)"),
            (3, 1.5, "标注数据\n(77个多边形)"),
            (5.5, 2, "地理配准\n投影变换"),
            (8, 2, "正射影像\n标签生成")
        ]
        
        arrows = [
            ((1.5, 2), (2.5, 2.5)),
            ((1.5, 2), (2.5, 1.5)),
            ((3.5, 2.5), (5, 2.2)),
            ((3.5, 1.5), (5, 1.8)),
            ((6, 2), (7.5, 2))
        ]
    else:
        boxes = [
            (1, 2, "Original Images\n(34 DJI Images)"),
            (3, 2.5, "Metadata Extraction\n(GPS+Camera Params)"),
            (3, 1.5, "Annotation Data\n(77 Polygons)"),
            (5.5, 2, "Georeferencing\nProjection Transform"),
            (8, 2, "Orthophoto\nLabel Generation")
        ]
        
        arrows = [
            ((1.5, 2), (2.5, 2.5)),
            ((1.5, 2), (2.5, 1.5)),
            ((3.5, 2.5), (5, 2.2)),
            ((3.5, 1.5), (5, 1.8)),
            ((6, 2), (7.5, 2))
        ]
    
    # 绘制框
    for x, y, text in boxes:
        rect = Rectangle((x-0.4, y-0.3), 0.8, 0.6, 
                        facecolor='lightblue', edgecolor='black', linewidth=2)
        ax.add_patch(rect)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # 绘制箭头
    for (x1, y1), (x2, y2) in arrows:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=2, color='red'))
    
    title = "工作流程示意图" if chinese_ok else "Workflow Diagram"
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)

def show_original_image_sample(ax, chinese_ok):
    """显示原始图像示例"""
    # 尝试读取第一张图像
    sample_image = None
    for i in range(1, 5):
        image_path = f'DJI_20250111173221_000{i}_V.JPG'
        if os.path.exists(image_path):
            sample_image = cv2.imread(image_path)
            if sample_image is not None:
                sample_image = cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB)
                # 缩放图像
                h, w = sample_image.shape[:2]
                scale = min(400/w, 300/h)
                new_w, new_h = int(w*scale), int(h*scale)
                sample_image = cv2.resize(sample_image, (new_w, new_h))
                break
    
    if sample_image is not None:
        ax.imshow(sample_image)
        title = "原始影像示例" if chinese_ok else "Original Image Sample"
    else:
        ax.text(0.5, 0.5, '原始图像\n(示例)' if chinese_ok else 'Original Image\n(Sample)', 
               ha='center', va='center', transform=ax.transAxes, fontsize=12)
        title = "原始影像" if chinese_ok else "Original Image"
    
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.axis('off')

def show_annotation_sample(ax, chinese_ok):
    """显示标注示例"""
    # 创建示例标注图
    sample_annotation = np.ones((300, 400, 3), dtype=np.uint8) * 200
    
    # 绘制几个示例多边形
    polygons = [
        np.array([[50, 50], [150, 60], [140, 120], [60, 110]], dtype=np.int32),
        np.array([[200, 80], [350, 90], [340, 180], [210, 170]], dtype=np.int32),
        np.array([[100, 200], [200, 210], [190, 280], [110, 270]], dtype=np.int32)
    ]
    
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]
    
    for polygon, color in zip(polygons, colors):
        cv2.fillPoly(sample_annotation, [polygon], color)
        cv2.polylines(sample_annotation, [polygon], True, (0, 0, 0), 2)
    
    ax.imshow(sample_annotation)
    title = "标注数据示例" if chinese_ok else "Annotation Data Sample"
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.axis('off')

def create_processing_stats_chart(ax, voting_stats, chinese_ok):
    """创建处理统计图表"""
    if chinese_ok:
        labels = ['总像素', '投票像素', '冲突像素', '标注像素']
    else:
        labels = ['Total Pixels', 'Voted Pixels', 'Conflict Pixels', 'Annotated Pixels']
    
    values = [
        voting_stats['total_pixels'],
        voting_stats['voted_pixels'],
        voting_stats['conflict_pixels'],
        voting_stats['annotated_pixels']
    ]
    
    colors = ['lightblue', 'orange', 'red', 'green']
    bars = ax.bar(labels, values, color=colors, alpha=0.7)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
               f'{value:,}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    title = "处理统计" if chinese_ok else "Processing Statistics"
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.set_ylabel('像素数量' if chinese_ok else 'Pixel Count')
    
    # 旋转x轴标签
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

def create_coverage_pie_chart(ax, voting_stats, chinese_ok):
    """创建覆盖率饼图"""
    if chinese_ok:
        labels = ['房屋标注', '背景区域']
    else:
        labels = ['House Annotation', 'Background']
    
    sizes = [voting_stats['annotated_pixels'], voting_stats['background_pixels']]
    colors = ['red', 'lightblue']
    
    wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                     autopct='%1.1f%%', startangle=90)
    
    title = "标注覆盖率" if chinese_ok else "Annotation Coverage"
    ax.set_title(title, fontsize=12, fontweight='bold')

def create_geo_info_table(ax, geo_info, voting_stats, chinese_ok):
    """创建地理信息表格"""
    ax.axis('off')
    
    if chinese_ok:
        table_data = [
            ['项目', '数值'],
            ['处理图像数', f"{voting_stats['processed_images']} 张"],
            ['正射影像尺寸', f"{geo_info['width']} × {geo_info['height']} 像素"],
            ['地理分辨率', f"{geo_info['resolution']} 度/像素"],
            ['经度范围', f"{geo_info['bounds'][0]:.6f}° - {geo_info['bounds'][2]:.6f}°"],
            ['纬度范围', f"{geo_info['bounds'][1]:.6f}° - {geo_info['bounds'][3]:.6f}°"],
            ['标注覆盖率', f"{voting_stats['annotated_percentage']:.2f}%"],
            ['坐标系统', geo_info['crs']]
        ]
        title = "地理信息摘要"
    else:
        table_data = [
            ['Item', 'Value'],
            ['Processed Images', f"{voting_stats['processed_images']} images"],
            ['Orthophoto Size', f"{geo_info['width']} × {geo_info['height']} pixels"],
            ['Resolution', f"{geo_info['resolution']} deg/pixel"],
            ['Longitude Range', f"{geo_info['bounds'][0]:.6f}° - {geo_info['bounds'][2]:.6f}°"],
            ['Latitude Range', f"{geo_info['bounds'][1]:.6f}° - {geo_info['bounds'][3]:.6f}°"],
            ['Annotation Coverage', f"{voting_stats['annotated_percentage']:.2f}%"],
            ['Coordinate System', geo_info['crs']]
        ]
        title = "Geographic Information Summary"
    
    table = ax.table(cellText=table_data[1:], colLabels=table_data[0],
                    cellLoc='left', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 1.5)
    
    # 设置表格样式
    for i in range(len(table_data)):
        for j in range(2):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title(title, fontsize=12, fontweight='bold', pad=20)

def create_technical_explanation(ax, chinese_ok):
    """创建技术说明"""
    ax.axis('off')
    
    if chinese_ok:
        explanation = """
技术实现要点：

1. 元数据提取：从DJI图像EXIF中提取GPS坐标、飞行高度、相机焦距等关键参数

2. 地理配准：建立像素坐标与地理坐标的对应关系，实现坐标系统转换

3. 投影变换：将原始影像中的标注数据反投影到统一的正射影像空间

4. 多标签冲突解决：对同一正射像元的多个标注进行投票，取多数决定最终分类

5. 质量控制：通过投票机制过滤不一致标注，提高分类结果的可靠性

核心算法：像素级投票 + 地理坐标转换 + 多图像融合
        """
    else:
        explanation = """
Technical Implementation Key Points:

1. Metadata Extraction: Extract GPS coordinates, flight altitude, camera focal length from DJI image EXIF

2. Georeferencing: Establish correspondence between pixel coordinates and geographic coordinates

3. Projection Transformation: Reproject annotation data from original images to unified orthophoto space

4. Multi-label Conflict Resolution: Vote on multiple annotations for the same orthophoto pixel

5. Quality Control: Filter inconsistent annotations through voting mechanism to improve reliability

Core Algorithm: Pixel-level Voting + Geographic Coordinate Transformation + Multi-image Fusion
        """
    
    ax.text(0.05, 0.95, explanation, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow'))

def main():
    """主函数"""
    print("开始创建正射影像工作流程可视化...")
    create_workflow_visualization()
    print("可视化完成！")

if __name__ == "__main__":
    main()
