#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版可视化系统 - 基于调试结果的改进版本
"""

import os
import json
import numpy as np
import cv2
from PIL import Image

# 地理处理相关导入
try:
    import rasterio
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio可用")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio不可用")

class FixedVisualizationSystem:
    def __init__(self):
        self.dom_data = None
        self.dom_segmentation = None
        
        # 改进的颜色映射
        self.color_map = {
            0: (0, 0, 0),         # 背景 - 黑色
            1: (0, 255, 255),     # 房子 - 青色（更明显）
        }
    
    def load_data(self):
        """加载DOM和分割数据"""
        print("📂 正在加载数据...")
        
        # 加载DOM数据
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                print(f"   ✅ DOM数据加载成功: {self.dom_data.shape}")
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
        
        # 加载分割掩码
        mask_file = 'manual_dom_segmentation_mask.png'
        if not os.path.exists(mask_file):
            print("❌ 分割掩码文件不存在")
            return False
        
        try:
            self.dom_segmentation = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
            print(f"   ✅ 分割掩码加载成功: {self.dom_segmentation.shape}")
            
            # 检查分割结果
            unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
            total_pixels = self.dom_segmentation.size
            
            print(f"   📊 分割统计:")
            for label, count in zip(unique_labels, counts):
                percentage = (count / total_pixels) * 100
                label_name = 'Background' if label == 0 else 'House'
                print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
            
        except Exception as e:
            print(f"❌ 加载分割掩码失败: {e}")
            return False
        
        return True
    
    def create_enhanced_dom_rgb(self):
        """创建增强的DOM RGB图像"""
        print("🎨 创建增强DOM RGB图像...")
        
        if self.dom_data.shape[0] < 3:
            print("❌ DOM波段数不足")
            return False
        
        # 使用前3个波段
        dom_rgb = np.transpose(self.dom_data[:3], (1, 2, 0))
        height, width, _ = dom_rgb.shape
        
        # 创建增强的RGB图像
        enhanced_rgb = np.zeros((height, width, 3), dtype=np.uint8)
        
        for i in range(3):
            band = dom_rgb[:, :, i].astype(np.float32)
            
            # 只处理非零像素
            valid_mask = band > 0
            
            if np.any(valid_mask):
                # 使用5%-95%百分位数进行对比度增强
                p5, p95 = np.percentile(band[valid_mask], [5, 95])
                
                if p95 > p5:
                    # 线性拉伸
                    band_enhanced = np.zeros_like(band)
                    band_enhanced[valid_mask] = np.clip((band[valid_mask] - p5) / (p95 - p5) * 255, 0, 255)
                    enhanced_rgb[:, :, i] = band_enhanced.astype(np.uint8)
                else:
                    enhanced_rgb[:, :, i] = band.astype(np.uint8)
        
        # 保存增强RGB图像
        enhanced_bgr = cv2.cvtColor(enhanced_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_dom_rgb_enhanced.png', enhanced_bgr)
        print("   ✅ 增强DOM RGB已保存: fixed_dom_rgb_enhanced.png")
        
        return enhanced_rgb
    
    def create_high_contrast_segmentation(self):
        """创建高对比度分割图像"""
        print("🎯 创建高对比度分割图像...")
        
        height, width = self.dom_segmentation.shape
        
        # 1. 纯色分割图
        segmentation_color = np.zeros((height, width, 3), dtype=np.uint8)
        
        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_color[mask] = color
        
        segmentation_bgr = cv2.cvtColor(segmentation_color, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_segmentation_high_contrast.png', segmentation_bgr)
        print("   ✅ 高对比度分割图已保存: fixed_segmentation_high_contrast.png")
        
        # 2. 只显示房子区域（白色背景）
        house_only = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
        house_mask = self.dom_segmentation == 1
        house_only[house_mask] = (255, 0, 0)  # 红色房子
        
        house_only_bgr = cv2.cvtColor(house_only, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_house_regions_white_bg.png', house_only_bgr)
        print("   ✅ 房子区域图（白底）已保存: fixed_house_regions_white_bg.png")
        
        return segmentation_color
    
    def create_multiple_overlays(self):
        """创建多种叠加效果"""
        print("🔄 创建多种叠加效果...")
        
        # 获取增强的DOM RGB
        enhanced_rgb = self.create_enhanced_dom_rgb()
        if enhanced_rgb is None:
            return False
        
        house_mask = self.dom_segmentation == 1
        
        # 1. 强对比叠加（50%透明度）
        overlay1 = enhanced_rgb.copy().astype(np.float32)
        overlay1[house_mask] = overlay1[house_mask] * 0.5 + np.array([255, 255, 0], dtype=np.float32) * 0.5
        overlay1 = np.clip(overlay1, 0, 255).astype(np.uint8)
        
        overlay1_bgr = cv2.cvtColor(overlay1, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_overlay_50_percent.png', overlay1_bgr)
        print("   ✅ 50%叠加图已保存: fixed_overlay_50_percent.png")
        
        # 2. 边界叠加
        overlay2 = enhanced_rgb.copy()
        
        # 提取房子区域的边界
        house_mask_uint8 = (house_mask * 255).astype(np.uint8)
        edges = cv2.Canny(house_mask_uint8, 50, 150)
        
        # 膨胀边界使其更明显
        kernel = np.ones((3, 3), np.uint8)
        edges_thick = cv2.dilate(edges, kernel, iterations=2)
        
        # 在边界上绘制红色
        edge_mask = edges_thick > 0
        overlay2[edge_mask] = (255, 0, 0)  # 红色边界
        
        overlay2_bgr = cv2.cvtColor(overlay2, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_overlay_boundaries.png', overlay2_bgr)
        print("   ✅ 边界叠加图已保存: fixed_overlay_boundaries.png")
        
        # 3. 填充叠加（房子区域完全替换）
        overlay3 = enhanced_rgb.copy()
        overlay3[house_mask] = (0, 255, 255)  # 青色填充
        
        overlay3_bgr = cv2.cvtColor(overlay3, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_overlay_filled.png', overlay3_bgr)
        print("   ✅ 填充叠加图已保存: fixed_overlay_filled.png")
        
        return True
    
    def create_zoomed_samples(self):
        """创建局部放大样本"""
        print("🔍 创建局部放大样本...")
        
        house_mask = self.dom_segmentation == 1
        
        # 找到房子区域的边界框
        house_coords = np.where(house_mask)
        if len(house_coords[0]) == 0:
            print("   ⚠️ 没有找到房子区域")
            return False
        
        min_row, max_row = np.min(house_coords[0]), np.max(house_coords[0])
        min_col, max_col = np.min(house_coords[1]), np.max(house_coords[1])
        
        # 扩展边界框
        margin = 200
        min_row = max(0, min_row - margin)
        max_row = min(self.dom_segmentation.shape[0], max_row + margin)
        min_col = max(0, min_col - margin)
        max_col = min(self.dom_segmentation.shape[1], max_col + margin)
        
        print(f"   📐 房子区域边界框: 行{min_row}-{max_row}, 列{min_col}-{max_col}")
        
        # 提取局部区域
        local_segmentation = self.dom_segmentation[min_row:max_row, min_col:max_col]
        
        if self.dom_data.shape[0] >= 3:
            local_dom_rgb = np.transpose(self.dom_data[:3, min_row:max_row, min_col:max_col], (1, 2, 0))
            
            # 标准化局部DOM
            local_enhanced = np.zeros_like(local_dom_rgb, dtype=np.uint8)
            for i in range(3):
                band = local_dom_rgb[:, :, i].astype(np.float32)
                valid_mask = band > 0
                
                if np.any(valid_mask):
                    p5, p95 = np.percentile(band[valid_mask], [5, 95])
                    if p95 > p5:
                        band_enhanced = np.zeros_like(band)
                        band_enhanced[valid_mask] = np.clip((band[valid_mask] - p5) / (p95 - p5) * 255, 0, 255)
                        local_enhanced[:, :, i] = band_enhanced.astype(np.uint8)
            
            # 创建局部叠加
            local_overlay = local_enhanced.copy().astype(np.float32)
            local_house_mask = local_segmentation == 1
            local_overlay[local_house_mask] = local_overlay[local_house_mask] * 0.6 + np.array([255, 255, 0], dtype=np.float32) * 0.4
            local_overlay = np.clip(local_overlay, 0, 255).astype(np.uint8)
            
            # 保存局部图像
            local_dom_bgr = cv2.cvtColor(local_enhanced, cv2.COLOR_RGB2BGR)
            cv2.imwrite('fixed_local_dom.png', local_dom_bgr)
            
            local_overlay_bgr = cv2.cvtColor(local_overlay, cv2.COLOR_RGB2BGR)
            cv2.imwrite('fixed_local_overlay.png', local_overlay_bgr)
            
            print("   ✅ 局部DOM已保存: fixed_local_dom.png")
            print("   ✅ 局部叠加已保存: fixed_local_overlay.png")
        
        # 保存局部分割掩码
        cv2.imwrite('fixed_local_segmentation.png', local_segmentation)
        print("   ✅ 局部分割掩码已保存: fixed_local_segmentation.png")
        
        return True
    
    def run_fixed_visualization(self):
        """运行修正版可视化"""
        print("=" * 80)
        print("🎨 修正版可视化系统")
        print("=" * 80)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 创建高对比度分割图
        segmentation_result = self.create_high_contrast_segmentation()
        if segmentation_result is None:
            return False
        
        # 3. 创建多种叠加效果
        overlay_result = self.create_multiple_overlays()
        if not overlay_result:
            return False

        # 4. 创建局部放大样本
        zoom_result = self.create_zoomed_samples()
        if not zoom_result:
            return False
        
        print("\n" + "=" * 80)
        print("🎨 修正版可视化完成！")
        print("=" * 80)
        print("📁 生成的修正版文件:")
        print("   • fixed_dom_rgb_enhanced.png - 增强DOM RGB")
        print("   • fixed_segmentation_high_contrast.png - 高对比度分割")
        print("   • fixed_house_regions_white_bg.png - 房子区域（白底）")
        print("   • fixed_overlay_50_percent.png - 50%透明叠加")
        print("   • fixed_overlay_boundaries.png - 边界叠加")
        print("   • fixed_overlay_filled.png - 填充叠加")
        print("   • fixed_local_dom.png - 局部DOM")
        print("   • fixed_local_overlay.png - 局部叠加")
        print("   • fixed_local_segmentation.png - 局部分割")
        
        print("\n🎨 可视化改进:")
        print("   ✅ 使用高对比度颜色")
        print("   ✅ 多种叠加效果")
        print("   ✅ 局部放大显示")
        print("   ✅ 边界检测和显示")
        print("   ✅ 增强的DOM图像")
        
        return True

def main():
    """主函数"""
    system = FixedVisualizationSystem()
    success = system.run_fixed_visualization()
    
    if success:
        print("\n🎉 修正版可视化完成！")
        print("💡 现在应该能看到清晰的房子区域分割结果了")
    else:
        print("\n❌ 修正版可视化失败，请检查错误信息")

if __name__ == "__main__":
    main()
