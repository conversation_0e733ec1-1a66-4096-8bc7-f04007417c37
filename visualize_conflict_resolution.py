#!/usr/bin/env python3
"""
多标签冲突解决结果可视化
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import os
from collections import Counter

def setup_chinese_font():
    """设置中文字体"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

def visualize_conflict_resolution_results():
    """可视化冲突解决结果"""
    chinese_ok = setup_chinese_font()
    
    # 检查文件是否存在
    required_files = [
        'dom_classification.png',
        'dom_classification_conflict_stats.json',
        'dom_classification_coverage_stats.json',
        'dom_classification_final_stats.json'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"缺少文件: {missing_files}")
        print("请先运行: python advanced_raster_generator.py")
        return
    
    # 读取数据
    raster = cv2.imread('dom_classification.png', cv2.IMREAD_GRAYSCALE)
    raster = (raster > 0).astype(np.uint8)
    
    with open('dom_classification_conflict_stats.json', 'r', encoding='utf-8') as f:
        conflict_stats = json.load(f)
    
    with open('dom_classification_coverage_stats.json', 'r', encoding='utf-8') as f:
        coverage_stats = json.load(f)
    
    with open('dom_classification_final_stats.json', 'r', encoding='utf-8') as f:
        final_stats = json.load(f)
    
    # 创建综合可视化
    fig = plt.figure(figsize=(20, 12))
    
    # 1. DOM分类结果
    ax1 = plt.subplot(2, 3, 1)
    im1 = ax1.imshow(raster, cmap='RdYlBu_r', interpolation='nearest')
    if chinese_ok:
        ax1.set_title('DOM分类结果\n(基于多标签冲突解决)', fontsize=12, fontweight='bold')
        ax1.set_xlabel('X (像素)')
        ax1.set_ylabel('Y (像素)')
    else:
        ax1.set_title('DOM Classification Result\n(Multi-label Conflict Resolution)', fontsize=12, fontweight='bold')
        ax1.set_xlabel('X (pixels)')
        ax1.set_ylabel('Y (pixels)')
    
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
    if chinese_ok:
        cbar1.set_label('分类标签')
    else:
        cbar1.set_label('Classification Label')
    
    # 2. 像素类型分布饼图
    ax2 = plt.subplot(2, 3, 2)
    pixel_types = ['无覆盖', '单图像', '多图像冲突'] if chinese_ok else ['No Coverage', 'Single Image', 'Multi-image Conflict']
    pixel_counts = [
        conflict_stats['no_vote_pixels'],
        conflict_stats['single_vote_pixels'],
        conflict_stats['conflict_pixels']
    ]
    colors = ['lightgray', 'lightblue', 'orange']
    
    wedges, texts, autotexts = ax2.pie(pixel_counts, labels=pixel_types, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    if chinese_ok:
        ax2.set_title('像素覆盖类型分布', fontsize=12, fontweight='bold')
    else:
        ax2.set_title('Pixel Coverage Type Distribution', fontsize=12, fontweight='bold')
    
    # 3. 冲突解决结果
    ax3 = plt.subplot(2, 3, 3)
    resolution_types = ['标注胜出', '背景胜出'] if chinese_ok else ['Annotation Wins', 'Background Wins']
    resolution_counts = [
        conflict_stats['annotation_wins'],
        conflict_stats['background_wins']
    ]
    colors = ['red', 'blue']
    
    bars = ax3.bar(resolution_types, resolution_counts, color=colors, alpha=0.7)
    if chinese_ok:
        ax3.set_title('冲突解决结果', fontsize=12, fontweight='bold')
        ax3.set_ylabel('像素数量')
    else:
        ax3.set_title('Conflict Resolution Results', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Pixel Count')
    
    # 添加数值标签
    for bar, count in zip(bars, resolution_counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(resolution_counts)*0.01,
                f'{count:,}', ha='center', va='bottom', fontweight='bold')
    
    # 4. 图像重叠分布
    ax4 = plt.subplot(2, 3, 4)
    overlap_counts = list(coverage_stats['overlap_distribution'].keys())
    overlap_pixels = list(coverage_stats['overlap_distribution'].values())
    
    bars = ax4.bar([str(x) for x in overlap_counts], overlap_pixels, 
                   color='green', alpha=0.7)
    if chinese_ok:
        ax4.set_title('图像重叠分布', fontsize=12, fontweight='bold')
        ax4.set_xlabel('重叠图像数量')
        ax4.set_ylabel('像素数量')
    else:
        ax4.set_title('Image Overlap Distribution', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Number of Overlapping Images')
        ax4.set_ylabel('Pixel Count')
    
    # 添加数值标签
    for bar, count in zip(bars, overlap_pixels):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(overlap_pixels)*0.01,
                f'{count:,}', ha='center', va='bottom', fontsize=8, rotation=45)
    
    # 5. 最终分类统计
    ax5 = plt.subplot(2, 3, 5)
    final_labels = ['背景', '房屋标注'] if chinese_ok else ['Background', 'House Annotation']
    final_counts = [final_stats['background_pixels'], final_stats['annotated_pixels']]
    final_colors = ['lightblue', 'red']
    
    wedges, texts, autotexts = ax5.pie(final_counts, labels=final_labels, colors=final_colors,
                                      autopct='%1.1f%%', startangle=90)
    if chinese_ok:
        ax5.set_title('最终DOM分类结果', fontsize=12, fontweight='bold')
    else:
        ax5.set_title('Final DOM Classification', fontsize=12, fontweight='bold')
    
    # 6. 处理统计表格
    ax6 = plt.subplot(2, 3, 6)
    ax6.axis('off')
    
    if chinese_ok:
        table_data = [
            ['统计项目', '数值'],
            ['总像素数', f"{conflict_stats['total_pixels']:,}"],
            ['冲突像素数', f"{conflict_stats['conflict_pixels']:,}"],
            ['最大重叠度', f"{coverage_stats['max_overlap']} 张图像"],
            ['处理图像数', f"{len(coverage_stats['image_coverage'])} 张"],
            ['标注覆盖率', f"{final_stats['annotated_percentage']:.2f}%"],
            ['冲突解决率', f"{(conflict_stats['conflict_pixels']/conflict_stats['total_pixels']*100):.2f}%"]
        ]
        title = '处理统计摘要'
    else:
        table_data = [
            ['Statistics', 'Value'],
            ['Total Pixels', f"{conflict_stats['total_pixels']:,}"],
            ['Conflict Pixels', f"{conflict_stats['conflict_pixels']:,}"],
            ['Max Overlap', f"{coverage_stats['max_overlap']} images"],
            ['Processed Images', f"{len(coverage_stats['image_coverage'])} images"],
            ['Annotation Coverage', f"{final_stats['annotated_percentage']:.2f}%"],
            ['Conflict Resolution Rate', f"{(conflict_stats['conflict_pixels']/conflict_stats['total_pixels']*100):.2f}%"]
        ]
        title = 'Processing Statistics Summary'
    
    table = ax6.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data)):
        for j in range(2):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax6.set_title(title, fontsize=12, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('conflict_resolution_analysis.png', dpi=300, bbox_inches='tight')
    print("冲突解决分析图已保存: conflict_resolution_analysis.png")
    plt.close()

def create_comparison_visualization():
    """创建简单方法vs高级方法的对比可视化"""
    chinese_ok = setup_chinese_font()
    
    # 检查文件
    if not all(os.path.exists(f) for f in ['labels.png', 'dom_classification.png']):
        print("缺少对比文件，请先运行两种方法")
        return
    
    # 读取两种方法的结果
    simple_raster = cv2.imread('labels.png', cv2.IMREAD_GRAYSCALE)
    simple_raster = (simple_raster > 0).astype(np.uint8)
    
    advanced_raster = cv2.imread('dom_classification.png', cv2.IMREAD_GRAYSCALE)
    advanced_raster = (advanced_raster > 0).astype(np.uint8)
    
    # 计算差异
    difference = advanced_raster.astype(int) - simple_raster.astype(int)
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 简单方法结果
    im1 = axes[0].imshow(simple_raster, cmap='RdYlBu_r', interpolation='nearest')
    if chinese_ok:
        axes[0].set_title('简单方法\n(取并集)', fontsize=14, fontweight='bold')
    else:
        axes[0].set_title('Simple Method\n(Union)', fontsize=14, fontweight='bold')
    axes[0].axis('off')
    
    # 高级方法结果
    im2 = axes[1].imshow(advanced_raster, cmap='RdYlBu_r', interpolation='nearest')
    if chinese_ok:
        axes[1].set_title('高级方法\n(投票机制)', fontsize=14, fontweight='bold')
    else:
        axes[1].set_title('Advanced Method\n(Voting Mechanism)', fontsize=14, fontweight='bold')
    axes[1].axis('off')
    
    # 差异图
    im3 = axes[2].imshow(difference, cmap='RdBu', vmin=-1, vmax=1, interpolation='nearest')
    if chinese_ok:
        axes[2].set_title('差异对比\n(高级-简单)', fontsize=14, fontweight='bold')
    else:
        axes[2].set_title('Difference\n(Advanced - Simple)', fontsize=14, fontweight='bold')
    axes[2].axis('off')
    
    # 添加颜色条
    cbar3 = plt.colorbar(im3, ax=axes[2], shrink=0.8)
    if chinese_ok:
        cbar3.set_label('差异值')
    else:
        cbar3.set_label('Difference Value')
    
    plt.tight_layout()
    plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')
    print("方法对比图已保存: method_comparison.png")
    plt.close()

def main():
    """主函数"""
    print("开始创建多标签冲突解决可视化...")
    
    # 创建冲突解决分析图
    visualize_conflict_resolution_results()
    
    # 创建方法对比图
    create_comparison_visualization()
    
    print("\n可视化完成！生成的文件:")
    print("- conflict_resolution_analysis.png: 冲突解决详细分析")
    print("- method_comparison.png: 简单方法vs高级方法对比")

if __name__ == "__main__":
    main()
