#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理匹配系统 - 将房子标签匹配到真实地理位置
"""

import os
import json
import numpy as np
import cv2
import urllib.parse
import re
from datetime import datetime

class GeographicMatchingSystem:
    def __init__(self):
        self.dom_image = None
        self.house_masks = {}
        self.flight_metadata = {}
        self.geographic_matches = {}
        
    def load_dom_and_masks(self):
        """加载DOM和房子掩码"""
        print("🗺️ 加载DOM和房子掩码...")
        
        # 1. 加载DOM
        dom_file = 'dom/0708_transparent_mosaic_group1.tif'
        if os.path.exists(dom_file):
            self.dom_image = cv2.imread(dom_file, cv2.IMREAD_COLOR)
            if self.dom_image is not None:
                print(f"   ✅ DOM加载成功: {self.dom_image.shape}")
            else:
                print("❌ DOM加载失败")
                return False
        else:
            print("❌ DOM文件不存在")
            return False
        
        # 2. 加载房子掩码
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 掩码统计文件不存在")
            return False
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        loaded_masks = 0
        for image_name, stats in stats_data.items():
            mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
            
            if os.path.exists(mask_file):
                mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    house_pixels = np.sum(mask == 1)
                    if house_pixels > 0:
                        self.house_masks[image_name] = {
                            'mask': mask,
                            'house_pixels': house_pixels,
                            'house_percentage': stats['house_percentage']
                        }
                        loaded_masks += 1
        
        print(f"   ✅ 加载了 {loaded_masks} 个房子掩码")
        return True
    
    def extract_flight_metadata(self):
        """提取飞行元数据"""
        print("✈️ 提取飞行元数据...")
        
        # 加载JSON标签获取空间信息
        label_file = 'label.json'
        if not os.path.exists(label_file):
            print("❌ label.json文件不存在")
            return False
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            
            for task in label_data:
                if 'data' in task and 'image' in task['data']:
                    image_path = task['data']['image']
                    clean_name = self.extract_clean_filename(image_path)
                    
                    if clean_name in self.house_masks and 'annotations' in task and task['annotations']:
                        annotation = task['annotations'][0]
                        if 'result' in annotation:
                            # 计算标注的空间特征
                            spatial_features = self.calculate_spatial_features(annotation['result'])
                            
                            # 提取时间信息
                            time_info = self.extract_time_info(clean_name)
                            
                            self.flight_metadata[clean_name] = {
                                'spatial_features': spatial_features,
                                'time_info': time_info,
                                'house_data': self.house_masks[clean_name]
                            }
            
            print(f"   ✅ 提取了 {len(self.flight_metadata)} 个图像的飞行元数据")
            return True
            
        except Exception as e:
            print(f"❌ 提取飞行元数据失败: {e}")
            return False
    
    def extract_clean_filename(self, image_path):
        """提取干净的文件名"""
        decoded_path = urllib.parse.unquote(image_path)
        
        if '\\' in decoded_path:
            filename = decoded_path.split('\\')[-1]
        elif '/' in decoded_path:
            filename = decoded_path.split('/')[-1]
        else:
            filename = decoded_path
        
        if filename.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
            filename = filename[2:]
        
        return filename
    
    def calculate_spatial_features(self, results):
        """计算空间特征"""
        features = {
            'centroids': [],
            'bounding_boxes': [],
            'areas': [],
            'total_area': 0
        }
        
        for result in results:
            if 'value' in result and 'points' in result['value']:
                points = result['value']['points']
                if len(points) >= 3:
                    # 计算重心
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    centroid_x = sum(x_coords) / len(x_coords)
                    centroid_y = sum(y_coords) / len(y_coords)
                    features['centroids'].append((centroid_x, centroid_y))
                    
                    # 计算边界框
                    min_x, max_x = min(x_coords), max(x_coords)
                    min_y, max_y = min(y_coords), max(y_coords)
                    features['bounding_boxes'].append((min_x, min_y, max_x, max_y))
                    
                    # 计算面积（简化）
                    area = (max_x - min_x) * (max_y - min_y)
                    features['areas'].append(area)
                    features['total_area'] += area
        
        # 计算整体重心
        if features['centroids']:
            avg_x = sum(c[0] for c in features['centroids']) / len(features['centroids'])
            avg_y = sum(c[1] for c in features['centroids']) / len(features['centroids'])
            features['overall_centroid'] = (avg_x, avg_y)
        else:
            features['overall_centroid'] = (50, 50)  # 默认中心
        
        return features
    
    def extract_time_info(self, image_name):
        """提取时间信息"""
        time_match = re.search(r'(\d{8})(\d{6})', image_name)
        seq_match = re.search(r'_(\d+)_?V?\.JPG', image_name)
        
        time_info = {}
        
        if time_match:
            date_str, time_str = time_match.groups()
            try:
                dt = datetime.strptime(f"{date_str}{time_str}", "%Y%m%d%H%M%S")
                time_info['datetime'] = dt
                time_info['timestamp'] = dt.timestamp()
            except:
                time_info['timestamp'] = 0
        
        if seq_match:
            time_info['sequence'] = int(seq_match.group(1))
        else:
            time_info['sequence'] = 0
        
        return time_info
    
    def create_geographic_matching(self):
        """创建地理匹配"""
        print("🎯 创建地理匹配...")
        
        # 按时间排序
        sorted_metadata = sorted(self.flight_metadata.items(), 
                               key=lambda x: x[1]['time_info']['timestamp'])
        
        print(f"   📊 按时间排序了 {len(sorted_metadata)} 个图像")
        
        # 分析飞行模式
        flight_pattern = self.analyze_flight_pattern(sorted_metadata)
        
        # 创建地理匹配
        dom_height, dom_width = self.dom_image.shape[:2]
        
        # 分析空间分布
        spatial_bounds = self.calculate_spatial_bounds(sorted_metadata)
        
        print(f"   📐 DOM尺寸: {dom_width} x {dom_height}")
        print(f"   📊 空间边界: {spatial_bounds}")
        
        for i, (image_name, metadata) in enumerate(sorted_metadata):
            # 计算地理位置
            geographic_pos = self.calculate_geographic_position(
                metadata, spatial_bounds, dom_width, dom_height, i, len(sorted_metadata)
            )
            
            self.geographic_matches[image_name] = {
                'dom_position': geographic_pos,
                'metadata': metadata,
                'flight_order': i
            }
            
            print(f"   📍 {i+1:2d}. {image_name}: DOM位置({geographic_pos['x']:.0f}, {geographic_pos['y']:.0f})")
        
        print(f"   ✅ 创建了 {len(self.geographic_matches)} 个地理匹配")
        return True
    
    def analyze_flight_pattern(self, sorted_metadata):
        """分析飞行模式"""
        print("   ✈️ 分析飞行模式...")
        
        # 分析空间重心的变化模式
        centroids = []
        for image_name, metadata in sorted_metadata:
            centroid = metadata['spatial_features']['overall_centroid']
            centroids.append(centroid)
        
        # 检测飞行模式（网格、螺旋、线性等）
        if len(centroids) > 1:
            # 计算相邻点的距离变化
            distances = []
            for i in range(1, len(centroids)):
                dist = np.sqrt((centroids[i][0] - centroids[i-1][0])**2 + 
                             (centroids[i][1] - centroids[i-1][1])**2)
                distances.append(dist)
            
            avg_distance = np.mean(distances)
            distance_std = np.std(distances)
            
            print(f"      平均距离: {avg_distance:.1f}, 标准差: {distance_std:.1f}")
            
            if distance_std < avg_distance * 0.5:
                pattern = "regular_grid"
            else:
                pattern = "irregular_path"
        else:
            pattern = "single_point"
        
        print(f"      检测到飞行模式: {pattern}")
        return pattern
    
    def calculate_spatial_bounds(self, sorted_metadata):
        """计算空间边界"""
        all_centroids = []
        for image_name, metadata in sorted_metadata:
            centroid = metadata['spatial_features']['overall_centroid']
            all_centroids.append(centroid)
        
        if all_centroids:
            x_coords = [c[0] for c in all_centroids]
            y_coords = [c[1] for c in all_centroids]
            
            return {
                'min_x': min(x_coords),
                'max_x': max(x_coords),
                'min_y': min(y_coords),
                'max_y': max(y_coords),
                'range_x': max(x_coords) - min(x_coords),
                'range_y': max(y_coords) - min(y_coords)
            }
        else:
            return {'min_x': 0, 'max_x': 100, 'min_y': 0, 'max_y': 100, 'range_x': 100, 'range_y': 100}
    
    def calculate_geographic_position(self, metadata, spatial_bounds, dom_width, dom_height, index, total):
        """计算地理位置"""
        centroid = metadata['spatial_features']['overall_centroid']
        
        # 基于空间重心的位置
        if spatial_bounds['range_x'] > 0 and spatial_bounds['range_y'] > 0:
            norm_x = (centroid[0] - spatial_bounds['min_x']) / spatial_bounds['range_x']
            norm_y = (centroid[1] - spatial_bounds['min_y']) / spatial_bounds['range_y']
        else:
            # 如果空间范围为0，使用时间序列
            grid_size = int(np.ceil(np.sqrt(total)))
            norm_x = (index % grid_size) / max(grid_size - 1, 1)
            norm_y = (index // grid_size) / max(grid_size - 1, 1)
        
        # 映射到DOM坐标，留出边界
        margin = 0.1
        effective_width = dom_width * (1 - 2 * margin)
        effective_height = dom_height * (1 - 2 * margin)
        
        dom_x = margin * dom_width + norm_x * effective_width
        dom_y = margin * dom_height + norm_y * effective_height
        
        return {
            'x': dom_x,
            'y': dom_y,
            'norm_x': norm_x,
            'norm_y': norm_y
        }
    
    def apply_geographic_matching(self):
        """应用地理匹配"""
        print("🎨 应用地理匹配...")
        
        dom_height, dom_width = self.dom_image.shape[:2]
        matched_overlay = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        total_matched_pixels = 0
        successful_matches = 0
        
        for image_name, match_data in self.geographic_matches.items():
            print(f"   🎯 匹配: {image_name}")
            
            dom_pos = match_data['dom_position']
            house_data = match_data['metadata']['house_data']
            
            # 计算覆盖区域
            coverage_size = min(300, dom_width // 12, dom_height // 12)
            
            start_x = max(0, int(dom_pos['x'] - coverage_size // 2))
            end_x = min(dom_width, start_x + coverage_size)
            start_y = max(0, int(dom_pos['y'] - coverage_size // 2))
            end_y = min(dom_height, start_y + coverage_size)
            
            # 执行匹配
            matched_pixels = self.execute_geographic_match(
                house_data['mask'], matched_overlay, start_y, end_y, start_x, end_x
            )
            
            total_matched_pixels += matched_pixels
            if matched_pixels > 0:
                successful_matches += 1
            
            print(f"      ✅ DOM({start_x}-{end_x}, {start_y}-{end_y}) -> 匹配{matched_pixels:,}像素")
        
        print(f"\n   📊 地理匹配统计:")
        print(f"      成功匹配: {successful_matches}/{len(self.geographic_matches)} 个图像")
        print(f"      总匹配像素: {total_matched_pixels:,}")
        
        # 保存匹配结果
        self.save_geographic_matching_results(matched_overlay)
        
        return total_matched_pixels > 0
    
    def execute_geographic_match(self, house_mask, overlay, start_y, end_y, start_x, end_x):
        """执行地理匹配"""
        actual_height = end_y - start_y
        actual_width = end_x - start_x
        
        if actual_height <= 0 or actual_width <= 0:
            return 0
        
        # 高质量缩放
        scaled_mask = cv2.resize(house_mask, (actual_width, actual_height), 
                               interpolation=cv2.INTER_LANCZOS4)
        
        # 保守的二值化
        scaled_mask = (scaled_mask > 32).astype(np.uint8)
        
        # 应用到叠加层
        overlay_region = overlay[start_y:end_y, start_x:end_x]
        house_pixels = scaled_mask == 1
        overlay_region[house_pixels] = 1
        
        return np.sum(house_pixels)
    
    def save_geographic_matching_results(self, matched_overlay):
        """保存地理匹配结果"""
        print("💾 保存地理匹配结果...")
        
        # 创建匹配结果
        matched_result = self.dom_image.copy()
        house_mask = matched_overlay == 1
        matched_result[house_mask] = [0, 255, 255]  # 黄色房子
        
        # 保存完整结果
        cv2.imwrite('geographic_matching_result.png', matched_result)
        
        # 创建多尺寸版本
        scales = [0.05, 0.1, 0.2, 0.5]
        for scale in scales:
            small_height = int(matched_result.shape[0] * scale)
            small_width = int(matched_result.shape[1] * scale)
            
            if small_height > 0 and small_width > 0:
                small_result = cv2.resize(matched_result, (small_width, small_height), 
                                        interpolation=cv2.INTER_AREA)
                cv2.imwrite(f'geographic_matching_{int(scale*100)}percent.png', small_result)
                print(f"   ✅ {int(scale*100)}%版本: geographic_matching_{int(scale*100)}percent.png ({small_width}x{small_height})")
        
        # 创建对比图
        dom_small = cv2.resize(self.dom_image, (600, 450), interpolation=cv2.INTER_AREA)
        matched_small = cv2.resize(matched_result, (600, 450), interpolation=cv2.INTER_AREA)
        
        comparison = np.hstack([dom_small, matched_small])
        cv2.imwrite('geographic_matching_comparison.png', comparison)
        
        # 添加标签
        comparison_labeled = comparison.copy()
        cv2.putText(comparison_labeled, 'Original DOM', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(comparison_labeled, 'Geographic Matched', (610, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.imwrite('geographic_matching_comparison_labeled.png', comparison_labeled)
        
        # 保存匹配信息
        house_pixels = int(np.sum(matched_overlay == 1))
        total_pixels = matched_overlay.shape[0] * matched_overlay.shape[1]
        
        matching_info = {
            'geographic_matching_method': 'spatial_temporal_analysis',
            'dom_size': [int(self.dom_image.shape[1]), int(self.dom_image.shape[0])],
            'num_images_matched': len(self.geographic_matches),
            'total_house_pixels': house_pixels,
            'house_percentage': float(house_pixels / total_pixels * 100),
            'matching_success': house_pixels > 0
        }
        
        with open('geographic_matching_info.json', 'w', encoding='utf-8') as f:
            json.dump(matching_info, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 地理匹配结果已保存: geographic_matching_result.png")
        print("   ✅ 对比图已保存: geographic_matching_comparison_labeled.png")
        print("   ✅ 匹配信息已保存: geographic_matching_info.json")
    
    def run_geographic_matching(self):
        """运行地理匹配系统"""
        print("=" * 80)
        print("🌍 地理匹配系统 - 将房子标签匹配到真实地理位置")
        print("=" * 80)
        
        # 1. 加载DOM和掩码
        if not self.load_dom_and_masks():
            print("❌ 步骤1失败")
            return False
        
        # 2. 提取飞行元数据
        if not self.extract_flight_metadata():
            print("❌ 步骤2失败")
            return False
        
        # 3. 创建地理匹配
        if not self.create_geographic_matching():
            print("❌ 步骤3失败")
            return False
        
        # 4. 应用地理匹配
        if not self.apply_geographic_matching():
            print("❌ 步骤4失败")
            return False
        
        print("\n" + "=" * 80)
        print("🌍 地理匹配完成！")
        print("=" * 80)
        print("📁 生成的地理匹配文件:")
        print("   • geographic_matching_result.png - 地理匹配结果")
        print("   • geographic_matching_comparison_labeled.png - 对比图 ⭐强烈推荐")
        print("   • geographic_matching_5percent.png - 5%版本")
        print("   • geographic_matching_10percent.png - 10%版本")
        print("   • geographic_matching_20percent.png - 20%版本")
        print("   • geographic_matching_50percent.png - 50%版本")
        print("   • geographic_matching_info.json - 匹配信息")
        
        print("\n🌍 地理匹配特点:")
        print("   ✅ 基于真实DOM图像")
        print("   ✅ 分析飞行时间序列")
        print("   ✅ 计算空间重心分布")
        print("   ✅ 匹配到真实地理位置")
        print("   ✅ 黄色标记房子区域")
        
        return True

def main():
    """主函数"""
    system = GeographicMatchingSystem()
    success = system.run_geographic_matching()
    
    if success:
        print("\n🎉 地理匹配成功！")
        print("📋 实现的匹配:")
        print("   ✅ 分析了飞行元数据")
        print("   ✅ 计算了空间特征")
        print("   ✅ 匹配到真实地理位置")
        print("   ✅ 叠加到DOM图像")
        print("\n💡 强烈建议查看: geographic_matching_comparison_labeled.png")
    else:
        print("\n❌ 地理匹配失败")

if __name__ == "__main__":
    main()
