#!/usr/bin/env python3
"""
标注反投影系统
将原始DJI图像上的标注映射到正射DOM影像上
"""

import json
import numpy as np
import cv2
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import os
import math
from collections import defaultdict
import matplotlib.pyplot as plt

class AnnotationReprojector:
    def __init__(self, image_dir=".", label_file="label.json"):
        self.image_dir = image_dir
        self.label_file = label_file
        self.image_metadata = {}
        self.annotations = {}
        self.dom_bounds = None
        self.dom_resolution = None
        self.dom_size = None
        
    def extract_gps_from_exif(self, exif_data):
        """从EXIF数据中提取GPS信息"""
        gps_info = {}

        # 直接从exif_data中查找GPSInfo
        gps_data = None
        for tag_id, value in exif_data.items():
            if tag_id == 'GPSInfo' or tag_id == 34853:  # GPSInfo的tag ID
                gps_data = value
                break

        if gps_data:
            # 提取纬度
            if 2 in gps_data and 1 in gps_data:
                lat_dms = gps_data[2]
                lat_ref = gps_data[1]
                lat = self.dms_to_decimal(lat_dms)
                if lat_ref == 'S':
                    lat = -lat
                gps_info['latitude'] = lat

            # 提取经度
            if 4 in gps_data and 3 in gps_data:
                lon_dms = gps_data[4]
                lon_ref = gps_data[3]
                lon = self.dms_to_decimal(lon_dms)
                if lon_ref == 'W':
                    lon = -lon
                gps_info['longitude'] = lon

            # 提取高度
            if 6 in gps_data:
                gps_info['altitude'] = float(gps_data[6])

        return gps_info
    
    def dms_to_decimal(self, dms):
        """将度分秒转换为十进制度"""
        degrees, minutes, seconds = dms
        return float(degrees) + float(minutes)/60 + float(seconds)/3600
    
    def load_image_metadata(self):
        """加载所有图像的元数据"""
        print("正在加载图像元数据...")
        
        for filename in os.listdir(self.image_dir):
            if filename.lower().endswith(('.jpg', '.jpeg')) and filename.startswith('DJI_'):
                filepath = os.path.join(self.image_dir, filename)
                
                try:
                    # 读取EXIF数据
                    with Image.open(filepath) as img:
                        exif_data = img._getexif()
                        if exif_data:
                            # 提取GPS信息
                            gps_info = self.extract_gps_from_exif(exif_data)
                            
                            # 提取相机参数
                            focal_length = exif_data.get(37386, 24)  # 默认24mm
                            if isinstance(focal_length, tuple):
                                focal_length = focal_length[0] / focal_length[1]
                            
                            # 获取图像尺寸
                            width, height = img.size
                            
                            self.image_metadata[filename] = {
                                'gps': gps_info,
                                'focal_length': focal_length,
                                'width': width,
                                'height': height,
                                'filepath': filepath
                            }
                            
                            print(f"已处理: {filename}")
                        
                except Exception as e:
                    print(f"处理 {filename} 时出错: {e}")
        
        print(f"成功加载 {len(self.image_metadata)} 张图像的元数据")
    
    def load_annotations(self):
        """加载标注数据（Label Studio格式）"""
        print("正在加载标注数据...")

        if not os.path.exists(self.label_file):
            print(f"标注文件 {self.label_file} 不存在")
            return

        with open(self.label_file, 'r', encoding='utf-8') as f:
            label_data = json.load(f)

        # 解析Label Studio格式的标注数据
        for item in label_data:
            # 从data字段中提取图像名称
            image_path = item['data']['image']
            # 提取文件名（去掉路径前缀和URL参数）
            if '?' in image_path:
                # 处理类似 '/data/local-files/?d=1.WORK%5Clabel-studio%5Cimages%5CDJI_20250111173221_0001_V.JPG' 的路径
                image_name = image_path.split('=')[-1].replace('%5C', '/')
                image_name = os.path.basename(image_name)
            else:
                image_name = os.path.basename(image_path)

            if image_name in self.image_metadata:
                annotations = []

                # 处理annotations字段
                for annotation_group in item.get('annotations', []):
                    for result in annotation_group.get('result', []):
                        if result['type'] == 'polygonlabels':
                            # 提取多边形坐标
                            points_data = result['value']['points']
                            # 转换百分比坐标为像素坐标
                            img_width = result['original_width']
                            img_height = result['original_height']

                            points = []
                            for point in points_data:
                                x = point[0] * img_width / 100.0  # 百分比转像素
                                y = point[1] * img_height / 100.0
                                points.append((x, y))

                            # 提取标签
                            labels = result['value'].get('polygonlabels', ['building'])
                            label = labels[0] if labels else 'building'

                            annotations.append({
                                'type': 'polygon',
                                'points': points,
                                'label': label
                            })

                if annotations:
                    self.annotations[image_name] = annotations
                    print(f"加载标注: {image_name} ({len(annotations)} 个标注)")

        print(f"成功加载 {len(self.annotations)} 张图像的标注数据")
    
    def calculate_dom_bounds(self):
        """计算DOM影像的地理边界"""
        print("正在计算DOM边界...")
        
        lats = []
        lons = []
        
        for metadata in self.image_metadata.values():
            gps = metadata.get('gps', {})
            if 'latitude' in gps and 'longitude' in gps:
                lats.append(gps['latitude'])
                lons.append(gps['longitude'])
        
        if not lats or not lons:
            print("没有找到有效的GPS数据")
            return
        
        # 计算边界，添加一些缓冲区
        min_lat, max_lat = min(lats), max(lats)
        min_lon, max_lon = min(lons), max(lons)
        
        lat_buffer = (max_lat - min_lat) * 0.1
        lon_buffer = (max_lon - min_lon) * 0.1
        
        self.dom_bounds = (
            min_lon - lon_buffer,  # west
            min_lat - lat_buffer,  # south
            max_lon + lon_buffer,  # east
            max_lat + lat_buffer   # north
        )
        
        print(f"DOM边界: {self.dom_bounds}")
    
    def create_dom_grid(self, target_size=(2000, 2000)):
        """创建DOM网格"""
        if not self.dom_bounds:
            self.calculate_dom_bounds()
        
        west, south, east, north = self.dom_bounds
        
        # 计算分辨率
        lon_range = east - west
        lat_range = north - south
        
        self.dom_resolution = max(lon_range / target_size[0], lat_range / target_size[1])
        
        # 计算实际尺寸
        self.dom_size = (
            int(lon_range / self.dom_resolution),
            int(lat_range / self.dom_resolution)
        )
        
        print(f"DOM尺寸: {self.dom_size}")
        print(f"DOM分辨率: {self.dom_resolution} 度/像素")
    
    def geo_to_pixel(self, lon, lat):
        """地理坐标转像素坐标"""
        if not self.dom_bounds or not self.dom_resolution:
            return None, None
        
        west, south, east, north = self.dom_bounds
        
        x = int((lon - west) / self.dom_resolution)
        y = int((north - lat) / self.dom_resolution)  # 注意Y轴翻转
        
        return x, y
    
    def pixel_to_geo(self, x, y):
        """像素坐标转地理坐标"""
        if not self.dom_bounds or not self.dom_resolution:
            return None, None
        
        west, south, east, north = self.dom_bounds
        
        lon = west + x * self.dom_resolution
        lat = north - y * self.dom_resolution  # 注意Y轴翻转
        
        return lon, lat
    
    def image_pixel_to_geo(self, image_name, img_x, img_y):
        """将图像像素坐标转换为地理坐标（简化版本）"""
        if image_name not in self.image_metadata:
            return None, None
        
        metadata = self.image_metadata[image_name]
        gps = metadata.get('gps', {})
        
        if 'latitude' not in gps or 'longitude' not in gps:
            return None, None
        
        # 简化的坐标转换（假设图像中心对应GPS坐标）
        center_lat = gps['latitude']
        center_lon = gps['longitude']
        
        # 估算像素到地理坐标的比例
        # 这里使用简化的线性近似
        img_width = metadata['width']
        img_height = metadata['height']
        altitude = gps.get('altitude', 100)  # 默认100米
        focal_length = metadata['focal_length']
        
        # 计算地面分辨率（米/像素）
        sensor_width = 23.5  # DJI相机传感器宽度（mm）
        gsd = (altitude * sensor_width) / (focal_length * img_width) / 1000  # 转换为米
        
        # 转换为度（粗略估算）
        meters_per_degree = 111000  # 1度约等于111km
        degrees_per_pixel = gsd / meters_per_degree
        
        # 计算相对于图像中心的偏移
        dx_pixels = img_x - img_width / 2
        dy_pixels = img_y - img_height / 2
        
        # 转换为地理坐标
        lon = center_lon + dx_pixels * degrees_per_pixel
        lat = center_lat - dy_pixels * degrees_per_pixel  # 注意Y轴方向
        
        return lon, lat
    
    def reproject_annotations(self):
        """将标注反投影到DOM上"""
        print("正在反投影标注...")
        
        if not self.dom_size:
            self.create_dom_grid()
        
        # 创建DOM标签图像
        dom_labels = np.zeros(self.dom_size[::-1], dtype=np.uint8)  # (height, width)
        dom_votes = np.zeros(self.dom_size[::-1], dtype=np.int32)   # 投票计数
        
        annotation_count = 0
        
        for image_name, annotations in self.annotations.items():
            print(f"处理图像: {image_name}")
            
            for annotation in annotations:
                if annotation['type'] == 'polygon':
                    # 转换多边形坐标
                    geo_points = []
                    for img_x, img_y in annotation['points']:
                        lon, lat = self.image_pixel_to_geo(image_name, img_x, img_y)
                        if lon is not None and lat is not None:
                            dom_x, dom_y = self.geo_to_pixel(lon, lat)
                            if (0 <= dom_x < self.dom_size[0] and 
                                0 <= dom_y < self.dom_size[1]):
                                geo_points.append([dom_x, dom_y])
                    
                    if len(geo_points) >= 3:  # 至少需要3个点形成多边形
                        # 在DOM上绘制多边形
                        poly_array = np.array(geo_points, dtype=np.int32)
                        cv2.fillPoly(dom_labels, [poly_array], 255)
                        cv2.fillPoly(dom_votes, [poly_array], 1)
                        annotation_count += 1
        
        print(f"成功反投影 {annotation_count} 个标注")
        
        return dom_labels, dom_votes
    
    def generate_dom_segmentation(self):
        """生成DOM分割结果"""
        print("开始生成DOM分割...")
        
        # 加载数据
        self.load_image_metadata()
        self.load_annotations()
        
        # 反投影标注
        dom_labels, dom_votes = self.reproject_annotations()
        
        # 保存结果
        cv2.imwrite('dom_segmentation.png', dom_labels)
        
        # 保存统计信息
        stats = {
            'dom_size': self.dom_size,
            'dom_bounds': self.dom_bounds,
            'dom_resolution': self.dom_resolution,
            'total_pixels': int(np.prod(self.dom_size)),
            'annotated_pixels': int(np.sum(dom_labels > 0)),
            'annotation_coverage': float(np.sum(dom_labels > 0) / np.prod(self.dom_size) * 100),
            'processed_images': len(self.image_metadata),
            'annotated_images': len(self.annotations)
        }
        
        with open('dom_segmentation_stats.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        print("DOM分割完成！")
        print(f"- DOM尺寸: {self.dom_size}")
        print(f"- 标注覆盖率: {stats['annotation_coverage']:.2f}%")
        print(f"- 处理图像: {stats['processed_images']} 张")
        print(f"- 有标注图像: {stats['annotated_images']} 张")
        
        return dom_labels, stats

def main():
    """主函数"""
    print("=== DJI图像标注反投影系统 ===")
    
    # 创建反投影器
    reprojector = AnnotationReprojector()
    
    # 生成DOM分割
    dom_labels, stats = reprojector.generate_dom_segmentation()
    
    print("\n结果文件:")
    print("- dom_segmentation.png: DOM分割结果")
    print("- dom_segmentation_stats.json: 统计信息")

if __name__ == "__main__":
    main()
