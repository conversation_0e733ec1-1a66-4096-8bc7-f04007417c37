#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度DOM语义分割系统 - 提高分割质量和视觉效果
"""

import os
import json
import numpy as np
import cv2
from scipy import ndimage

# 地理处理相关导入
try:
    import rasterio
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio和pyproj可用")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio或pyproj不可用")

class HighPrecisionDOMSegmentation:
    def __init__(self):
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.geographic_annotations = {}
        self.dom_segmentation = None
        self.transformer = None
        
        # 使用更鲜明的颜色映射
        self.label_colors = {
            'Background': 0,
            'Deciduous Vegetation (2)': 1,
            'False Positive-Confusing (1)': 2
        }
        
        self.color_map = {
            0: (0, 0, 0),         # 背景 - 黑色
            1: (0, 255, 0),       # Deciduous Vegetation - 亮绿色
            2: (255, 0, 0),       # False Positive-Confusing - 亮红色
        }
    
    def load_dom_data(self):
        """加载DOM数据"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM波段数: {src.count}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                
                # 创建坐标转换器
                self.transformer = Transformer.from_crs(self.dom_crs, 'EPSG:4326', always_xy=True)
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def load_annotations(self):
        """加载地理坐标标注数据"""
        print("📋 正在加载地理坐标标注数据...")
        
        geo_file = 'geographic_annotations.json'
        if os.path.exists(geo_file):
            try:
                with open(geo_file, 'r', encoding='utf-8') as f:
                    geo_data = json.load(f)
                self.geographic_annotations = geo_data.get('annotations', {})
                print(f"   ✅ 加载了 {len(self.geographic_annotations)} 张图像的地理标注数据")
                
                # 统计标签分布
                label_counts = {}
                for image_name, annotations in self.geographic_annotations.items():
                    for annotation in annotations:
                        if 'properties' in annotation and 'labels' in annotation['properties']:
                            labels = annotation['properties']['labels']
                            for label in labels:
                                label_counts[label] = label_counts.get(label, 0) + 1
                
                print(f"   📊 标签分布:")
                for label, count in label_counts.items():
                    print(f"      '{label}': {count} 个")
                
                return True
                
            except Exception as e:
                print(f"   ⚠️ 加载地理标注失败: {e}")
                return False
        
        return False
    
    def point_in_polygon(self, x, y, polygon_coords):
        """判断点是否在多边形内"""
        n = len(polygon_coords)
        inside = False
        
        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def get_label_for_point(self, lon, lat):
        """为给定地理坐标点获取标签"""
        # 遍历所有图像的所有标注
        for image_name, annotations in self.geographic_annotations.items():
            for annotation in annotations:
                if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                    coords = annotation['geometry']['coordinates'][0]
                    
                    if self.point_in_polygon(lon, lat, coords):
                        # 获取标签
                        if 'properties' in annotation and 'labels' in annotation['properties']:
                            labels = annotation['properties']['labels']
                            if labels:
                                label_name = labels[0]
                                return self.label_colors.get(label_name, 0)
        
        return 0  # 背景
    
    def map_labels_to_dom_high_precision(self):
        """高精度标签映射到DOM"""
        print("🎯 正在进行高精度标签映射...")
        
        bands, dom_height, dom_width = self.dom_data.shape
        print(f"   📐 DOM尺寸: {dom_width} x {dom_height}")
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
            print(f"   🎭 有效像元: {np.sum(valid_mask):,} / {dom_height * dom_width:,}")
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        # 使用更小的采样步长提高精度
        sample_step = 3  # 从10减少到3
        processed_pixels = 0
        total_pixels = 0
        
        # 计算需要处理的像元数
        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                if valid_mask[row, col]:
                    total_pixels += 1
        
        print(f"   📊 预计处理 {total_pixels:,} 个采样点 (高精度模式)...")
        
        successful_mappings = 0
        label_stats = {0: 0, 1: 0, 2: 0}
        
        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                if not valid_mask[row, col]:
                    continue
                
                # 转换为地理坐标
                utm_x, utm_y = self.dom_transform * (col, row)
                try:
                    geo_lon, geo_lat = self.transformer.transform(utm_x, utm_y)
                except:
                    continue
                
                # 直接获取标签
                label = self.get_label_for_point(geo_lon, geo_lat)
                
                if label > 0:
                    successful_mappings += 1
                    label_stats[label] += 1
                    
                    # 更精细的邻域填充
                    fill_radius = sample_step // 2
                    for r in range(max(0, row-fill_radius), min(dom_height, row+fill_radius+1)):
                        for c in range(max(0, col-fill_radius), min(dom_width, col+fill_radius+1)):
                            if valid_mask[r, c] and self.dom_segmentation[r, c] == 0:
                                self.dom_segmentation[r, c] = label
                
                processed_pixels += 1
                if processed_pixels % 10000 == 0:
                    progress = (processed_pixels / total_pixels) * 100
                    print(f"   📊 映射进度: {progress:.1f}% ({processed_pixels}/{total_pixels}), 成功映射: {successful_mappings}")
                    print(f"      当前标签分布: 背景={label_stats[0]}, 植被={label_stats[1]}, 混淆={label_stats[2]}")
        
        print(f"   ✅ 高精度标签映射完成，成功映射: {successful_mappings} 个采样点")
        print(f"   📊 采样点标签分布: 背景={label_stats[0]}, 植被={label_stats[1]}, 混淆={label_stats[2]}")
        
        # 后处理：形态学操作平滑边界
        print("   🔧 正在进行后处理...")
        self.post_process_segmentation()
        
        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_dom_pixels = dom_height * dom_width
        
        print(f"   📊 最终分割结果统计:")
        label_name_map = {v: k for k, v in self.label_colors.items()}
        for label, count in zip(unique_labels, counts):
            label_name = label_name_map.get(label, f"Unknown({label})")
            percentage = (count / total_dom_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        return True
    
    def post_process_segmentation(self):
        """后处理分割结果"""
        print("      🔧 应用形态学操作...")
        
        # 对每个标签类别进行形态学操作
        for label_id in [1, 2]:
            mask = (self.dom_segmentation == label_id)
            
            # 形态学闭运算：填充小洞
            kernel = np.ones((3, 3), np.uint8)
            mask_closed = cv2.morphologyEx(mask.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
            
            # 形态学开运算：去除小噪点
            mask_opened = cv2.morphologyEx(mask_closed, cv2.MORPH_OPEN, kernel)
            
            # 更新分割结果
            self.dom_segmentation[mask] = 0  # 先清除原有标签
            self.dom_segmentation[mask_opened.astype(bool)] = label_id  # 应用处理后的结果

    def save_results(self):
        """保存高质量分割结果"""
        print("💾 正在保存高质量分割结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码
        cv2.imwrite('high_precision_dom_segmentation_mask.png', self.dom_segmentation)

        # 创建高对比度彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        # 保存彩色分割结果
        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('high_precision_dom_segmentation_result.png', segmentation_bgr)

        # 创建改进的叠加可视化
        print("   🎨 创建改进的叠加可视化...")

        # 确保正确处理DOM数据
        if self.dom_data.shape[0] >= 3:
            # 使用RGB波段
            dom_rgb = np.transpose(self.dom_data[:3], (1, 2, 0))
            # 标准化到0-255范围
            for i in range(3):
                band = dom_rgb[:, :, i]
                band_min, band_max = np.percentile(band[band > 0], [2, 98])
                dom_rgb[:, :, i] = np.clip((band - band_min) / (band_max - band_min) * 255, 0, 255)
            dom_rgb = dom_rgb.astype(np.uint8)
        else:
            # 单波段转RGB
            dom_gray = self.dom_data[0]
            gray_min, gray_max = np.percentile(dom_gray[dom_gray > 0], [2, 98])
            dom_gray_norm = np.clip((dom_gray - gray_min) / (gray_max - gray_min) * 255, 0, 255).astype(np.uint8)
            dom_rgb = np.stack([dom_gray_norm, dom_gray_norm, dom_gray_norm], axis=2)

        # 创建半透明叠加
        overlay = dom_rgb.copy().astype(np.float32)

        # 只在有分割标签的地方叠加颜色
        for label_id, color in self.color_map.items():
            if label_id > 0:  # 跳过背景
                mask = self.dom_segmentation == label_id
                if np.any(mask):
                    overlay[mask] = overlay[mask] * 0.7 + np.array(color, dtype=np.float32) * 0.3

        overlay = np.clip(overlay, 0, 255).astype(np.uint8)
        overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('high_precision_dom_segmentation_overlay.png', overlay_bgr)

        # 创建边界叠加图
        print("   🔍 创建边界叠加图...")
        boundary_overlay = dom_rgb.copy()

        # 提取边界
        for label_id in [1, 2]:
            mask = (self.dom_segmentation == label_id).astype(np.uint8)
            if np.any(mask):
                # 使用Canny边缘检测
                edges = cv2.Canny(mask * 255, 50, 150)
                # 膨胀边界使其更明显
                kernel = np.ones((2, 2), np.uint8)
                edges = cv2.dilate(edges, kernel, iterations=1)

                # 在边界上绘制颜色
                color = self.color_map[label_id]
                boundary_overlay[edges > 0] = color

        boundary_overlay_bgr = cv2.cvtColor(boundary_overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('high_precision_dom_segmentation_boundary.png', boundary_overlay_bgr)

        print("   ✅ 分割掩码已保存: high_precision_dom_segmentation_mask.png")
        print("   ✅ 分割结果已保存: high_precision_dom_segmentation_result.png")
        print("   ✅ 叠加可视化已保存: high_precision_dom_segmentation_overlay.png")
        print("   ✅ 边界可视化已保存: high_precision_dom_segmentation_boundary.png")

    def run_high_precision_segmentation(self):
        """运行高精度DOM分割系统"""
        print("=" * 80)
        print("🎯 高精度DOM语义分割系统")
        print("=" * 80)

        # 1. 加载DOM数据
        if not self.load_dom_data():
            return False

        # 2. 加载地理坐标标注数据
        if not self.load_annotations():
            return False

        # 3. 执行高精度标签映射
        if not self.map_labels_to_dom_high_precision():
            return False

        # 4. 保存结果
        self.save_results()

        print("\n" + "=" * 80)
        print("🎯 高精度DOM语义分割完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • high_precision_dom_segmentation_result.png - 高对比度彩色分割结果")
        print("   • high_precision_dom_segmentation_mask.png - 灰度分割掩码")
        print("   • high_precision_dom_segmentation_overlay.png - 改进的DOM叠加可视化")
        print("   • high_precision_dom_segmentation_boundary.png - 边界可视化")

        print("\n🎯 高精度版本的改进:")
        print("   ✅ 采样步长从10减少到3，提高精度")
        print("   ✅ 更精细的邻域填充策略")
        print("   ✅ 形态学后处理平滑边界")
        print("   ✅ 改进的DOM图像标准化")
        print("   ✅ 半透明叠加效果")
        print("   ✅ 边界可视化")
        print("   ✅ 高对比度颜色映射")

        return True

def main():
    """主函数"""
    system = HighPrecisionDOMSegmentation()
    success = system.run_high_precision_segmentation()

    if success:
        print("\n🎉 高精度DOM分割任务成功完成！")
        print("📋 主要改进:")
        print("   ✅ 3倍提高的采样精度")
        print("   ✅ 形态学后处理")
        print("   ✅ 改进的可视化效果")
        print("   ✅ 多种输出格式")
        print("   ✅ 更清晰的边界定义")
    else:
        print("\n❌ 高精度DOM分割任务失败，请检查错误信息")

if __name__ == "__main__":
    main()
