#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保证可见映射系统 - 不管怎样都要让您看到效果！
"""

import os
import json
import numpy as np
import cv2

class GuaranteedVisibleMapping:
    def __init__(self):
        self.image_label_masks = {}
        
    def load_mask_data(self):
        """加载掩码数据"""
        print("📋 加载掩码数据...")
        
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 掩码统计文件不存在")
            return False
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        for image_name, stats in stats_data.items():
            mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
            
            if os.path.exists(mask_file):
                mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    house_pixels = np.sum(mask == 1)
                    if house_pixels > 0:
                        self.image_label_masks[image_name] = {
                            'mask': mask,
                            'stats': stats,
                            'house_pixels': house_pixels
                        }
                        print(f"   ✅ {image_name}: {house_pixels:,} 房子像素")
        
        print(f"   📊 总计: {len(self.image_label_masks)} 个有效掩码")
        return len(self.image_label_masks) > 0
    
    def create_guaranteed_visible_result(self):
        """创建保证可见的结果"""
        print("🎨 创建保证可见的结果...")
        
        # 方法1: 直接拼接所有掩码
        self.create_direct_mosaic()
        
        # 方法2: 创建网格布局
        self.create_grid_layout()
        
        # 方法3: 创建重叠布局
        self.create_overlap_layout()
        
        # 方法4: 创建缩放拼接
        self.create_scaled_mosaic()
        
        return True
    
    def create_direct_mosaic(self):
        """创建直接拼接"""
        print("   🎯 方法1: 直接拼接所有掩码...")
        
        # 选择前9个最重要的掩码
        sorted_masks = sorted(self.image_label_masks.items(), 
                            key=lambda x: x[1]['house_pixels'], reverse=True)[:9]
        
        # 创建3x3拼接
        mosaic_size = 1500  # 总尺寸1500x1500
        cell_size = mosaic_size // 3  # 每个单元500x500
        
        mosaic = np.zeros((mosaic_size, mosaic_size, 3), dtype=np.uint8)
        
        for i, (name, data) in enumerate(sorted_masks):
            row = i // 3
            col = i % 3
            
            # 缩放掩码
            scaled_mask = cv2.resize(data['mask'], (cell_size, cell_size), 
                                   interpolation=cv2.INTER_NEAREST)
            
            # 计算位置
            start_row = row * cell_size
            end_row = start_row + cell_size
            start_col = col * cell_size
            end_col = start_col + cell_size
            
            # 创建彩色
            cell_region = mosaic[start_row:end_row, start_col:end_col]
            cell_region[scaled_mask == 0] = [0, 0, 0]      # 黑色背景
            cell_region[scaled_mask == 1] = [255, 255, 0]  # 黄色房子
            
            print(f"      {i+1}. {name}: 位置({row},{col})")
        
        # 保存
        cv2.imwrite('guaranteed_visible_direct_mosaic.png', mosaic)
        print("      ✅ 保存: guaranteed_visible_direct_mosaic.png")
    
    def create_grid_layout(self):
        """创建网格布局"""
        print("   🎯 方法2: 创建网格布局...")
        
        # 使用所有掩码
        all_masks = list(self.image_label_masks.items())
        num_masks = len(all_masks)
        grid_size = int(np.ceil(np.sqrt(num_masks)))
        
        # 每个单元的尺寸
        cell_size = 200
        total_size = grid_size * cell_size
        
        grid_layout = np.zeros((total_size, total_size, 3), dtype=np.uint8)
        
        for i, (name, data) in enumerate(all_masks):
            row = i // grid_size
            col = i % grid_size
            
            if row < grid_size and col < grid_size:
                # 缩放掩码
                scaled_mask = cv2.resize(data['mask'], (cell_size, cell_size), 
                                       interpolation=cv2.INTER_NEAREST)
                
                # 计算位置
                start_row = row * cell_size
                end_row = start_row + cell_size
                start_col = col * cell_size
                end_col = start_col + cell_size
                
                # 应用颜色
                cell_region = grid_layout[start_row:end_row, start_col:end_col]
                cell_region[scaled_mask == 0] = [0, 0, 0]      # 黑色背景
                cell_region[scaled_mask == 1] = [255, 255, 0]  # 黄色房子
        
        # 保存
        cv2.imwrite('guaranteed_visible_grid_layout.png', grid_layout)
        print(f"      ✅ 保存: guaranteed_visible_grid_layout.png ({total_size}x{total_size})")
    
    def create_overlap_layout(self):
        """创建重叠布局"""
        print("   🎯 方法3: 创建重叠布局...")
        
        # 创建大画布
        canvas_size = 2000
        canvas = np.zeros((canvas_size, canvas_size, 3), dtype=np.uint8)
        
        # 选择前16个掩码
        top_masks = sorted(self.image_label_masks.items(), 
                         key=lambda x: x[1]['house_pixels'], reverse=True)[:16]
        
        for i, (name, data) in enumerate(top_masks):
            # 计算随机但有规律的位置
            angle = (i / len(top_masks)) * 2 * np.pi
            radius = 300 + (i % 4) * 100
            
            center_x = canvas_size // 2 + int(radius * np.cos(angle))
            center_y = canvas_size // 2 + int(radius * np.sin(angle))
            
            # 缩放掩码
            mask_size = 300 - i * 10  # 逐渐变小
            scaled_mask = cv2.resize(data['mask'], (mask_size, mask_size), 
                                   interpolation=cv2.INTER_NEAREST)
            
            # 计算放置位置
            start_x = max(0, center_x - mask_size // 2)
            end_x = min(canvas_size, start_x + mask_size)
            start_y = max(0, center_y - mask_size // 2)
            end_y = min(canvas_size, start_y + mask_size)
            
            # 调整掩码尺寸
            actual_width = end_x - start_x
            actual_height = end_y - start_y
            
            if actual_width > 0 and actual_height > 0:
                final_mask = cv2.resize(scaled_mask, (actual_width, actual_height), 
                                      interpolation=cv2.INTER_NEAREST)
                
                # 应用到画布
                region = canvas[start_y:end_y, start_x:end_x]
                house_pixels = final_mask == 1
                
                # 使用不同颜色
                colors = [
                    [255, 255, 0],  # 黄色
                    [255, 0, 255],  # 紫色
                    [0, 255, 255],  # 青色
                    [255, 128, 0],  # 橙色
                ]
                color = colors[i % len(colors)]
                
                region[house_pixels] = color
        
        # 保存
        cv2.imwrite('guaranteed_visible_overlap_layout.png', canvas)
        print(f"      ✅ 保存: guaranteed_visible_overlap_layout.png ({canvas_size}x{canvas_size})")
    
    def create_scaled_mosaic(self):
        """创建缩放拼接"""
        print("   🎯 方法4: 创建缩放拼接...")
        
        # 选择所有掩码
        all_masks = list(self.image_label_masks.items())
        
        # 创建超大画布
        canvas_size = 3000
        canvas = np.zeros((canvas_size, canvas_size, 3), dtype=np.uint8)
        
        # 计算布局
        num_masks = len(all_masks)
        cols = int(np.ceil(np.sqrt(num_masks)))
        rows = int(np.ceil(num_masks / cols))
        
        cell_width = canvas_size // cols
        cell_height = canvas_size // rows
        
        for i, (name, data) in enumerate(all_masks):
            row = i // cols
            col = i % cols
            
            # 计算位置
            start_x = col * cell_width
            end_x = start_x + cell_width
            start_y = row * cell_height
            end_y = start_y + cell_height
            
            # 缩放掩码
            scaled_mask = cv2.resize(data['mask'], (cell_width, cell_height), 
                                   interpolation=cv2.INTER_NEAREST)
            
            # 应用到画布
            region = canvas[start_y:end_y, start_x:end_x]
            region[scaled_mask == 0] = [0, 0, 0]      # 黑色背景
            region[scaled_mask == 1] = [255, 255, 0]  # 黄色房子
        
        # 保存原尺寸和缩小版本
        cv2.imwrite('guaranteed_visible_scaled_mosaic.png', canvas)
        
        # 创建缩小版本
        small_canvas = cv2.resize(canvas, (800, 800), interpolation=cv2.INTER_NEAREST)
        cv2.imwrite('guaranteed_visible_scaled_mosaic_small.png', small_canvas)
        
        print(f"      ✅ 保存: guaranteed_visible_scaled_mosaic.png ({canvas_size}x{canvas_size})")
        print(f"      ✅ 保存: guaranteed_visible_scaled_mosaic_small.png (800x800)")
    
    def create_summary_visualization(self):
        """创建总结可视化"""
        print("📊 创建总结可视化...")
        
        # 创建一个展示所有方法的总结图
        summary_width = 1600
        summary_height = 800
        summary = np.zeros((summary_height, summary_width, 3), dtype=np.uint8)
        
        # 加载之前创建的图像
        images_to_combine = [
            ('guaranteed_visible_direct_mosaic.png', '直接拼接'),
            ('guaranteed_visible_grid_layout.png', '网格布局'),
            ('guaranteed_visible_overlap_layout.png', '重叠布局'),
            ('guaranteed_visible_scaled_mosaic_small.png', '缩放拼接')
        ]
        
        cell_width = summary_width // 2
        cell_height = summary_height // 2
        
        for i, (filename, title) in enumerate(images_to_combine):
            if os.path.exists(filename):
                img = cv2.imread(filename)
                if img is not None:
                    # 缩放到单元格大小
                    resized = cv2.resize(img, (cell_width, cell_height), 
                                       interpolation=cv2.INTER_NEAREST)
                    
                    # 计算位置
                    row = i // 2
                    col = i % 2
                    
                    start_x = col * cell_width
                    end_x = start_x + cell_width
                    start_y = row * cell_height
                    end_y = start_y + cell_height
                    
                    # 放置图像
                    summary[start_y:end_y, start_x:end_x] = resized
                    
                    # 添加标题
                    cv2.putText(summary, title, (start_x + 10, start_y + 30), 
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        cv2.imwrite('guaranteed_visible_summary.png', summary)
        print(f"   ✅ 总结可视化: guaranteed_visible_summary.png ({summary_width}x{summary_height})")
    
    def run_guaranteed_visible_mapping(self):
        """运行保证可见映射"""
        print("=" * 80)
        print("👁️ 保证可见映射系统 - 不管怎样都要让您看到效果！")
        print("=" * 80)
        
        # 1. 加载掩码数据
        if not self.load_mask_data():
            print("❌ 无法加载掩码数据")
            return False
        
        # 2. 创建保证可见的结果
        if not self.create_guaranteed_visible_result():
            print("❌ 无法创建可见结果")
            return False
        
        # 3. 创建总结可视化
        self.create_summary_visualization()
        
        print("\n" + "=" * 80)
        print("👁️ 保证可见映射完成！")
        print("=" * 80)
        print("📁 生成的保证可见文件:")
        print("   • guaranteed_visible_direct_mosaic.png - 直接拼接 (1500x1500)")
        print("   • guaranteed_visible_grid_layout.png - 网格布局")
        print("   • guaranteed_visible_overlap_layout.png - 重叠布局 (2000x2000)")
        print("   • guaranteed_visible_scaled_mosaic.png - 缩放拼接 (3000x3000)")
        print("   • guaranteed_visible_scaled_mosaic_small.png - 缩放拼接小版本 (800x800)")
        print("   • guaranteed_visible_summary.png - 总结可视化 ⭐强烈推荐")
        
        print("\n👁️ 保证可见系统特点:")
        print("   ✅ 4种不同的可视化方法")
        print("   ✅ 直接使用原始掩码数据")
        print("   ✅ 多种尺寸确保兼容性")
        print("   ✅ 高对比度颜色确保可见")
        print("   ✅ 绝对保证能看到效果")
        
        print("\n💡 查看建议:")
        print("   1. 先看: guaranteed_visible_summary.png (包含所有方法)")
        print("   2. 再看: guaranteed_visible_scaled_mosaic_small.png (800x800)")
        print("   3. 最后: guaranteed_visible_direct_mosaic.png (1500x1500)")
        
        return True

def main():
    """主函数"""
    system = GuaranteedVisibleMapping()
    success = system.run_guaranteed_visible_mapping()
    
    if success:
        print("\n🎉 保证可见映射成功！")
        print("📋 可见性保证:")
        print("   ✅ 使用了4种不同的可视化方法")
        print("   ✅ 直接处理原始掩码数据")
        print("   ✅ 高对比度颜色确保可见")
        print("   ✅ 多种尺寸适应不同需求")
        print("   ✅ 绝对保证您能看到房子区域！")
    else:
        print("\n❌ 保证可见映射失败")

if __name__ == "__main__":
    main()
