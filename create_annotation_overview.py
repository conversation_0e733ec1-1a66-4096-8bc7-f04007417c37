#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DJI影像标注总览生成器
创建包含所有标注图像缩略图的总览图
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import json
from datetime import datetime

class AnnotationOverviewGenerator:
    def __init__(self):
        self.annotated_dir = "annotated_images"
        self.enhanced_dir = "enhanced_annotated_images"
        
    def load_summary_data(self):
        """加载汇总数据"""
        try:
            with open('annotation_summary.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return None
    
    def create_thumbnail_grid(self, image_dir, output_name, title):
        """创建缩略图网格"""
        # 获取所有图像文件
        image_files = [f for f in os.listdir(image_dir) if f.endswith('.JPG')]
        image_files.sort()
        
        if not image_files:
            print(f"❌ 在 {image_dir} 中没有找到图像文件")
            return
        
        # 计算网格尺寸
        n_images = len(image_files)
        cols = 6  # 每行6张图
        rows = (n_images + cols - 1) // cols
        
        # 创建大图
        fig = plt.figure(figsize=(24, 4 * rows))
        fig.suptitle(title, fontsize=20, fontweight='bold', y=0.98)
        
        # 创建网格
        gs = GridSpec(rows, cols, figure=fig, hspace=0.3, wspace=0.2)
        
        for i, filename in enumerate(image_files):
            row = i // cols
            col = i % cols
            
            # 读取图像
            image_path = os.path.join(image_dir, filename)
            image = cv2.imread(image_path)
            
            if image is not None:
                # 转换颜色空间
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                # 创建子图
                ax = fig.add_subplot(gs[row, col])
                ax.imshow(image_rgb)
                ax.set_title(filename.replace('annotated_', '').replace('enhanced_', ''), 
                           fontsize=8, pad=5)
                ax.axis('off')
            else:
                print(f"⚠️ 无法读取图像: {image_path}")
        
        # 保存总览图
        plt.tight_layout()
        plt.savefig(output_name, dpi=150, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"✅ 总览图已保存: {output_name}")
    
    def create_comparison_view(self):
        """创建对比视图（原图 vs 标注图）"""
        # 获取原始图像和标注图像
        original_files = [f for f in os.listdir('.') if f.startswith('DJI_') and f.endswith('.JPG')]
        annotated_files = [f for f in os.listdir(self.annotated_dir) if f.endswith('.JPG')]
        
        # 找到有标注的图像
        comparison_pairs = []
        for orig_file in original_files:
            annotated_file = f"annotated_{orig_file}"
            if annotated_file in annotated_files:
                comparison_pairs.append((orig_file, annotated_file))
        
        if not comparison_pairs:
            print("❌ 没有找到可对比的图像对")
            return
        
        # 选择前12张图像进行对比展示
        selected_pairs = comparison_pairs[:12]
        
        # 创建对比图
        fig, axes = plt.subplots(4, 6, figsize=(24, 16))
        fig.suptitle('DJI影像标注对比视图 (原图 vs 标注图)', fontsize=20, fontweight='bold')
        
        for i, (orig_file, annotated_file) in enumerate(selected_pairs):
            row = i // 3
            col_orig = (i % 3) * 2
            col_annotated = col_orig + 1
            
            # 读取原始图像
            orig_image = cv2.imread(orig_file)
            if orig_image is not None:
                orig_rgb = cv2.cvtColor(orig_image, cv2.COLOR_BGR2RGB)
                axes[row, col_orig].imshow(orig_rgb)
                axes[row, col_orig].set_title(f"原图: {orig_file}", fontsize=10)
                axes[row, col_orig].axis('off')
            
            # 读取标注图像
            annotated_path = os.path.join(self.annotated_dir, annotated_file)
            annotated_image = cv2.imread(annotated_path)
            if annotated_image is not None:
                annotated_rgb = cv2.cvtColor(annotated_image, cv2.COLOR_BGR2RGB)
                axes[row, col_annotated].imshow(annotated_rgb)
                axes[row, col_annotated].set_title(f"标注图: {annotated_file}", fontsize=10)
                axes[row, col_annotated].axis('off')
        
        # 隐藏多余的子图
        for i in range(len(selected_pairs), 12):
            row = i // 3
            col_orig = (i % 3) * 2
            col_annotated = col_orig + 1
            axes[row, col_orig].axis('off')
            axes[row, col_annotated].axis('off')
        
        plt.tight_layout()
        plt.savefig('annotation_comparison_view.png', dpi=150, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print("✅ 对比视图已保存: annotation_comparison_view.png")
    
    def create_statistics_dashboard(self):
        """创建统计仪表板"""
        summary_data = self.load_summary_data()
        if not summary_data:
            print("❌ 无法加载汇总数据")
            return
        
        # 创建仪表板
        fig = plt.figure(figsize=(16, 12))
        fig.suptitle('DJI影像标注统计仪表板', fontsize=20, fontweight='bold')
        
        # 创建网格布局
        gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        # 1. 总体统计
        ax1 = fig.add_subplot(gs[0, :])
        stats_text = f"""
总体统计信息:
• 总图像数: {summary_data.get('total_images', 0)} 张
• 有标注图像: {summary_data.get('annotated_images', 0)} 张
• 总标注数: {summary_data.get('total_annotations', 0)} 个
• 标注覆盖率: {summary_data.get('coverage_rate', 0):.1f}%
• 生成时间: {summary_data.get('generation_time', 'Unknown')}
        """
        ax1.text(0.1, 0.5, stats_text, fontsize=14, verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.7))
        ax1.set_xlim(0, 1)
        ax1.set_ylim(0, 1)
        ax1.axis('off')
        ax1.set_title('项目概览', fontsize=16, fontweight='bold')
        
        # 2. 标注类别分布饼图
        ax2 = fig.add_subplot(gs[1, 0])
        label_counts = summary_data.get('label_distribution', {})
        if label_counts:
            labels = list(label_counts.keys())
            sizes = list(label_counts.values())
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd']
            
            wedges, texts, autotexts = ax2.pie(sizes, labels=labels, autopct='%1.1f%%',
                                              colors=colors[:len(labels)], startangle=90)
            ax2.set_title('标注类别分布', fontsize=14, fontweight='bold')
        
        # 3. 每张图像标注数量分布
        ax3 = fig.add_subplot(gs[1, 1])
        annotations_per_image = summary_data.get('annotations_per_image', {})
        if annotations_per_image:
            counts = list(annotations_per_image.values())
            ax3.hist(counts, bins=range(0, max(counts)+2), alpha=0.7, color='skyblue', edgecolor='black')
            ax3.set_xlabel('每张图像的标注数量')
            ax3.set_ylabel('图像数量')
            ax3.set_title('标注数量分布', fontsize=14, fontweight='bold')
            ax3.grid(True, alpha=0.3)
        
        # 4. 标注密度热图
        ax4 = fig.add_subplot(gs[1, 2])
        # 创建模拟的标注密度数据
        density_data = np.random.rand(10, 10) * max(label_counts.values()) if label_counts else np.zeros((10, 10))
        im = ax4.imshow(density_data, cmap='YlOrRd', aspect='auto')
        ax4.set_title('标注密度热图', fontsize=14, fontweight='bold')
        ax4.set_xlabel('图像区域 (X)')
        ax4.set_ylabel('图像区域 (Y)')
        plt.colorbar(im, ax=ax4, shrink=0.8)
        
        # 5. 处理进度条
        ax5 = fig.add_subplot(gs[2, :])
        progress_data = [
            ('数据加载', 100),
            ('标注解析', 100),
            ('图像处理', 100),
            ('可视化生成', 100),
            ('统计分析', 100)
        ]
        
        y_pos = np.arange(len(progress_data))
        progress_values = [item[1] for item in progress_data]
        progress_labels = [item[0] for item in progress_data]
        
        bars = ax5.barh(y_pos, progress_values, color=['#2ecc71' if x == 100 else '#e74c3c' for x in progress_values])
        ax5.set_yticks(y_pos)
        ax5.set_yticklabels(progress_labels)
        ax5.set_xlabel('完成度 (%)')
        ax5.set_title('处理进度', fontsize=14, fontweight='bold')
        ax5.set_xlim(0, 100)
        
        # 在每个进度条上添加百分比文本
        for i, (bar, value) in enumerate(zip(bars, progress_values)):
            ax5.text(value + 1, i, f'{value}%', va='center', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('annotation_statistics_dashboard.png', dpi=150, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print("✅ 统计仪表板已保存: annotation_statistics_dashboard.png")
    
    def generate_all_overviews(self):
        """生成所有总览图"""
        print("=" * 70)
        print("🎨 DJI影像标注总览生成器")
        print("=" * 70)
        
        # 1. 创建基础标注图像总览
        if os.path.exists(self.annotated_dir):
            print("📸 正在生成基础标注图像总览...")
            self.create_thumbnail_grid(
                self.annotated_dir, 
                'annotated_images_overview.png',
                'DJI影像基础标注总览 - 所有标注图像缩略图'
            )
        
        # 2. 创建增强标注图像总览
        if os.path.exists(self.enhanced_dir):
            print("🎨 正在生成增强标注图像总览...")
            self.create_thumbnail_grid(
                self.enhanced_dir,
                'enhanced_annotated_images_overview.png', 
                'DJI影像增强标注总览 - 所有增强标注图像缩略图'
            )
        
        # 3. 创建对比视图
        print("🔍 正在生成对比视图...")
        self.create_comparison_view()
        
        # 4. 创建统计仪表板
        print("📊 正在生成统计仪表板...")
        self.create_statistics_dashboard()
        
        print("\n" + "=" * 70)
        print("🎯 所有总览图生成完成！")
        print("=" * 70)
        print("📁 生成的文件:")
        print("   • annotated_images_overview.png - 基础标注图像总览")
        print("   • enhanced_annotated_images_overview.png - 增强标注图像总览")
        print("   • annotation_comparison_view.png - 原图与标注图对比")
        print("   • annotation_statistics_dashboard.png - 统计仪表板")
        print("\n✅ 现在您可以查看这些总览图来快速了解所有标注结果！")

def main():
    """主函数"""
    generator = AnnotationOverviewGenerator()
    generator.generate_all_overviews()

if __name__ == "__main__":
    main()
