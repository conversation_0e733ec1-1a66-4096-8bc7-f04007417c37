#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成具有地理坐标的标签影像系统
将原始DJI影像的标注映射到正射影像上，生成带有地理坐标的标签影像
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import math
import os
from datetime import datetime

class GeoreferencedLabelGenerator:
    def __init__(self):
        self.image_metadata = {}
        self.annotations = {}
        self.dom_bounds = None
        self.dom_size = None
        self.dom_resolution = None
        self.label_mapping = {
            'False Positive-Confusing (1)': 1,
            'Deciduous Vegetation (2)': 2,
            'Building': 3,
            'Road': 4,
            'Water': 5
        }
        
    def load_image_metadata(self):
        """加载图像元数据"""
        print("🔍 正在加载图像元数据...")

        # 使用预定义的元数据（从之前成功运行的系统中获取）
        predefined_metadata = {
            'DJI_20250111173221_0001_V.JPG': {'latitude': 31.002295, 'longitude': 103.149542},
            'DJI_20250111173223_0002_V.JPG': {'latitude': 31.002345, 'longitude': 103.149503},
            'DJI_20250111173226_0003_V.JPG': {'latitude': 31.002495, 'longitude': 103.149380},
            'DJI_20250111173229_0004_V.JPG': {'latitude': 31.002651, 'longitude': 103.149252},
            'DJI_20250111173231_0005_V.JPG': {'latitude': 31.002808, 'longitude': 103.149124},
            'DJI_20250111173234_0006_V.JPG': {'latitude': 31.002963, 'longitude': 103.148996},
            'DJI_20250111173236_0007_V.JPG': {'latitude': 31.003122, 'longitude': 103.148901},
            'DJI_20250111173239_0008_V.JPG': {'latitude': 31.003260, 'longitude': 103.149004},
            'DJI_20250111173241_0009_V.JPG': {'latitude': 31.003358, 'longitude': 103.149159},
            'DJI_20250111173244_0010_V.JPG': {'latitude': 31.003275, 'longitude': 103.149285},
            'DJI_20250111173247_0011_V.JPG': {'latitude': 31.003119, 'longitude': 103.149415},
            'DJI_20250111173249_0012_V.JPG': {'latitude': 31.002963, 'longitude': 103.149542},
            'DJI_20250111173252_0013_V.JPG': {'latitude': 31.002806, 'longitude': 103.149671},
            'DJI_20250111173254_0014_V.JPG': {'latitude': 31.002662, 'longitude': 103.149799},
            'DJI_20250111173257_0015_V.JPG': {'latitude': 31.002690, 'longitude': 103.149930},
            'DJI_20250111173259_0016_V.JPG': {'latitude': 31.002827, 'longitude': 103.150067},
            'DJI_20250111173302_0017_V.JPG': {'latitude': 31.002987, 'longitude': 103.150062},
            'DJI_20250111173304_0018_V.JPG': {'latitude': 31.003144, 'longitude': 103.149938},
            'DJI_20250111173307_0019_V.JPG': {'latitude': 31.003300, 'longitude': 103.149811},
            'DJI_20250111173310_0020_V.JPG': {'latitude': 31.003456, 'longitude': 103.149683},
            'DJI_20250111173312_0021_V.JPG': {'latitude': 31.003615, 'longitude': 103.149561},
            'DJI_20250111173315_0022_V.JPG': {'latitude': 31.003762, 'longitude': 103.149607},
            'DJI_20250111173317_0023_V.JPG': {'latitude': 31.003887, 'longitude': 103.149764},
            'DJI_20250111173320_0024_V.JPG': {'latitude': 31.003853, 'longitude': 103.149900},
            'DJI_20250111173322_0025_V.JPG': {'latitude': 31.003707, 'longitude': 103.150023},
            'DJI_20250111173325_0026_V.JPG': {'latitude': 31.003552, 'longitude': 103.150150},
            'DJI_20250111173328_0027_V.JPG': {'latitude': 31.003395, 'longitude': 103.150278},
            'DJI_20250111173330_0028_V.JPG': {'latitude': 31.003257, 'longitude': 103.150408},
            'DJI_20250111173333_0029_V.JPG': {'latitude': 31.003300, 'longitude': 103.150538},
            'DJI_20250111173335_0030_V.JPG': {'latitude': 31.003441, 'longitude': 103.150674},
            'DJI_20250111173338_0031_V.JPG': {'latitude': 31.003601, 'longitude': 103.150652},
            'DJI_20250111173340_0032_V.JPG': {'latitude': 31.003758, 'longitude': 103.150527},
            'DJI_20250111173343_0033_V.JPG': {'latitude': 31.003915, 'longitude': 103.150398},
            'DJI_20250111173345_0034_V.JPG': {'latitude': 31.004000, 'longitude': 103.150329}
        }

        for filename in os.listdir('.'):
            if filename.startswith('DJI_') and filename.endswith('.JPG'):
                try:
                    # 获取图像尺寸
                    image = Image.open(filename)
                    width, height = image.size

                    # 使用预定义的GPS数据
                    if filename in predefined_metadata:
                        gps_data = predefined_metadata[filename]
                        lat = gps_data['latitude']
                        lon = gps_data['longitude']
                        altitude = 100  # 默认高度

                        self.image_metadata[filename] = {
                            'width': width,
                            'height': height,
                            'focal_length': 24.0,  # 默认焦距
                            'gps': {
                                'latitude': lat,
                                'longitude': lon,
                                'altitude': altitude
                            }
                        }

                        print(f"✅ {filename}: GPS({lat:.6f}, {lon:.6f})")
                    else:
                        print(f"⚠️ {filename}: 没有预定义的GPS信息")

                except Exception as e:
                    print(f"❌ 处理 {filename} 时出错: {e}")

        print(f"📊 成功加载 {len(self.image_metadata)} 张图像的元数据")
    
    def convert_gps_coordinate(self, coord, ref):
        """转换GPS坐标格式"""
        degrees = float(coord[0])
        minutes = float(coord[1])
        seconds = float(coord[2])
        
        decimal = degrees + minutes/60 + seconds/3600
        
        if ref in ['S', 'W']:
            decimal = -decimal
            
        return decimal
    
    def load_annotations(self):
        """加载标注数据"""
        print("🏷️ 正在加载标注数据...")

        try:
            with open('label.json', 'r', encoding='utf-8') as f:
                data = json.load(f)

            for item in data:
                # 提取图像文件名
                image_path = item['data']['image']
                raw_filename = os.path.basename(image_path)

                # 从URL编码的路径中提取真实文件名
                import urllib.parse
                decoded_path = urllib.parse.unquote(raw_filename)

                # 进一步提取文件名（处理可能的路径分隔符）
                if '\\' in decoded_path:
                    filename = decoded_path.split('\\')[-1]
                elif '/' in decoded_path:
                    filename = decoded_path.split('/')[-1]
                else:
                    filename = decoded_path

                # 调试信息
                print(f"🔍 处理文件: {raw_filename} -> {filename}")

                # 提取标注
                annotations = []
                if 'annotations' in item and item['annotations']:
                    for annotation in item['annotations']:
                        if 'result' in annotation:
                            for result in annotation['result']:
                                if result['type'] == 'polygonlabels':
                                    points = result['value']['points']
                                    labels = result['value']['polygonlabels']

                                    # 转换相对坐标为绝对坐标
                                    if filename in self.image_metadata:
                                        img_width = self.image_metadata[filename]['width']
                                        img_height = self.image_metadata[filename]['height']

                                        abs_points = []
                                        for point in points:
                                            x = (point[0] / 100.0) * img_width
                                            y = (point[1] / 100.0) * img_height
                                            abs_points.append([x, y])

                                        annotations.append({
                                            'type': 'polygon',
                                            'points': abs_points,
                                            'label': labels[0] if labels else 'Unknown'
                                        })

                                        print(f"   ✅ 添加标注: {labels[0] if labels else 'Unknown'}, {len(points)} 个点")
                                    else:
                                        print(f"   ⚠️ 文件 {filename} 不在元数据中")

                if annotations:
                    self.annotations[filename] = annotations
                    print(f"📝 {filename}: {len(annotations)} 个标注")
                else:
                    print(f"⚠️ {filename}: 没有找到标注")

            print(f"📋 成功加载 {len(self.annotations)} 张图像的标注数据")

        except Exception as e:
            print(f"❌ 加载标注数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def calculate_dom_bounds(self):
        """计算DOM边界"""
        print("🗺️ 正在计算DOM地理边界...")
        
        if not self.image_metadata:
            return
        
        lats = []
        lons = []
        
        for metadata in self.image_metadata.values():
            gps = metadata.get('gps', {})
            if 'latitude' in gps and 'longitude' in gps:
                lats.append(gps['latitude'])
                lons.append(gps['longitude'])
        
        if lats and lons:
            # 添加边界缓冲区
            buffer = 0.002  # 约200米缓冲区
            
            min_lat = min(lats) - buffer
            max_lat = max(lats) + buffer
            min_lon = min(lons) - buffer
            max_lon = max(lons) + buffer
            
            self.dom_bounds = [min_lon, min_lat, max_lon, max_lat]
            
            # 计算覆盖范围
            lat_range = max_lat - min_lat
            lon_range = max_lon - min_lon
            
            # 转换为米
            lat_meters = lat_range * 111000
            lon_meters = lon_range * 111000 * math.cos(math.radians((min_lat + max_lat) / 2))
            
            print(f"🌍 DOM边界: 经度[{min_lon:.6f}, {max_lon:.6f}], 纬度[{min_lat:.6f}, {max_lat:.6f}]")
            print(f"📏 覆盖范围: {lon_meters:.1f}m × {lat_meters:.1f}m")
    
    def create_dom_grid(self, target_resolution_cm=50):
        """创建DOM网格"""
        if not self.dom_bounds:
            self.calculate_dom_bounds()
        
        west, south, east, north = self.dom_bounds
        
        # 计算分辨率（度/像素）
        target_resolution_m = target_resolution_cm / 100.0
        lat_resolution = target_resolution_m / 111000
        lon_resolution = target_resolution_m / (111000 * math.cos(math.radians((south + north) / 2)))
        
        self.dom_resolution = min(lat_resolution, lon_resolution)
        
        # 计算DOM尺寸
        width = int((east - west) / self.dom_resolution)
        height = int((north - south) / self.dom_resolution)
        
        self.dom_size = (width, height)
        
        # 计算实际分辨率
        actual_resolution_m = self.dom_resolution * 111000
        
        print(f"🎯 DOM网格: {width} × {height} 像素")
        print(f"📐 实际分辨率: {actual_resolution_m * 100:.1f} cm/像素")
    
    def geo_to_pixel(self, lon, lat):
        """地理坐标转像素坐标"""
        if not self.dom_bounds or not self.dom_size:
            return None, None
        
        west, south, east, north = self.dom_bounds
        
        x = int((lon - west) / self.dom_resolution)
        y = int((north - lat) / self.dom_resolution)
        
        return x, y
    
    def image_pixel_to_geo(self, image_name, img_x, img_y):
        """图像像素坐标转地理坐标"""
        if image_name not in self.image_metadata:
            return None, None
        
        metadata = self.image_metadata[image_name]
        gps = metadata.get('gps', {})
        
        if 'latitude' not in gps or 'longitude' not in gps:
            return None, None
        
        center_lat = gps['latitude']
        center_lon = gps['longitude']
        altitude = gps.get('altitude', 100)
        focal_length = metadata['focal_length']
        img_width = metadata['width']
        img_height = metadata['height']
        
        # DJI M3E传感器参数
        sensor_width = 23.5   # mm
        sensor_height = 15.6  # mm
        
        # 计算地面采样距离（GSD）
        gsd_x = (altitude * sensor_width) / (focal_length * img_width) / 1000  # 米/像素
        gsd_y = (altitude * sensor_height) / (focal_length * img_height) / 1000
        
        # 转换为度
        meters_per_degree_lat = 111000
        meters_per_degree_lon = 111000 * math.cos(math.radians(center_lat))
        
        degrees_per_pixel_x = gsd_x / meters_per_degree_lon
        degrees_per_pixel_y = gsd_y / meters_per_degree_lat
        
        # 计算相对于图像中心的偏移
        dx_pixels = img_x - img_width / 2
        dy_pixels = img_y - img_height / 2
        
        # 转换为地理坐标
        lon = center_lon + dx_pixels * degrees_per_pixel_x
        lat = center_lat - dy_pixels * degrees_per_pixel_y
        
        return lon, lat

    def generate_georeferenced_labels(self):
        """生成具有地理坐标的标签影像"""
        print("🎨 正在生成地理坐标标签影像...")

        if not self.dom_size:
            self.create_dom_grid()

        # 创建标签图像
        label_image = np.zeros(self.dom_size[::-1], dtype=np.uint8)
        confidence_image = np.zeros(self.dom_size[::-1], dtype=np.float32)

        # 定义标签颜色
        label_colors = {
            0: (0, 0, 0),        # 背景 - 黑色
            1: (255, 0, 0),      # False Positive - 红色
            2: (0, 255, 0),      # Building - 绿色
            3: (0, 0, 255),      # Road - 蓝色
            4: (255, 255, 0),    # Water - 黄色
            5: (255, 0, 255)     # Vegetation - 紫色
        }

        # 创建彩色标签图像
        color_label_image = np.zeros((*self.dom_size[::-1], 3), dtype=np.uint8)

        annotation_count = 0

        for image_name, annotations in self.annotations.items():
            print(f"🖼️ 处理图像: {image_name}")

            for annotation in annotations:
                if annotation['type'] == 'polygon':
                    geo_points = []
                    for img_x, img_y in annotation['points']:
                        lon, lat = self.image_pixel_to_geo(image_name, img_x, img_y)
                        if lon is not None and lat is not None:
                            dom_x, dom_y = self.geo_to_pixel(lon, lat)
                            if (0 <= dom_x < self.dom_size[0] and
                                0 <= dom_y < self.dom_size[1]):
                                geo_points.append([dom_x, dom_y])

                    if len(geo_points) >= 3:
                        poly_array = np.array(geo_points, dtype=np.int32)

                        # 获取标签ID
                        label = annotation['label']
                        label_id = self.label_mapping.get(label, 1)

                        # 创建掩码
                        mask = np.zeros(self.dom_size[::-1], dtype=np.uint8)
                        cv2.fillPoly(mask, [poly_array], 1)

                        # 更新标签图像
                        label_image[mask == 1] = label_id

                        # 更新彩色标签图像
                        color = label_colors.get(label_id, (128, 128, 128))
                        color_label_image[mask == 1] = color

                        # 计算置信度
                        area = cv2.contourArea(poly_array)
                        confidence = min(1.0, area / 1000.0)
                        confidence_image[mask == 1] = confidence

                        annotation_count += 1

        print(f"✅ 成功处理 {annotation_count} 个标注")

        return label_image, color_label_image, confidence_image

    def create_comprehensive_visualization(self, label_image, color_label_image, confidence_image):
        """创建综合可视化"""
        print("🎨 正在创建可视化...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('DJI影像标注 → 地理坐标标签影像生成系统', fontsize=16, fontweight='bold')

        # 1. 原始DOM区域
        ax1 = axes[0, 0]
        background = np.ones(self.dom_size[::-1], dtype=np.uint8) * 200
        ax1.imshow(background, cmap='gray', alpha=0.7)
        ax1.set_title('DOM覆盖区域与影像中心点', fontsize=14)
        ax1.set_xlabel('像素 X (东西方向)')
        ax1.set_ylabel('像素 Y (南北方向)')

        # 添加图像中心点
        for image_name, metadata in self.image_metadata.items():
            gps = metadata.get('gps', {})
            if 'latitude' in gps and 'longitude' in gps:
                dom_x, dom_y = self.geo_to_pixel(gps['longitude'], gps['latitude'])
                if (0 <= dom_x < self.dom_size[0] and 0 <= dom_y < self.dom_size[1]):
                    ax1.plot(dom_x, dom_y, 'ro', markersize=4, alpha=0.8)

        # 2. 生成的标签影像（彩色）
        ax2 = axes[0, 1]
        ax2.imshow(color_label_image)
        ax2.set_title('生成的地理坐标标签影像', fontsize=14)
        ax2.set_xlabel('像素 X (东西方向)')
        ax2.set_ylabel('像素 Y (南北方向)')

        # 添加图例
        legend_elements = [
            mpatches.Patch(color='red', label='False Positive'),
            mpatches.Patch(color='green', label='Building'),
            mpatches.Patch(color='blue', label='Road'),
            mpatches.Patch(color='yellow', label='Water'),
            mpatches.Patch(color='magenta', label='Vegetation')
        ]
        ax2.legend(handles=legend_elements, loc='upper right')

        # 3. 标签ID图像
        ax3 = axes[1, 0]
        im3 = ax3.imshow(label_image, cmap='tab10', vmin=0, vmax=5)
        ax3.set_title('标签ID分布图', fontsize=14)
        ax3.set_xlabel('像素 X (东西方向)')
        ax3.set_ylabel('像素 Y (南北方向)')
        plt.colorbar(im3, ax=ax3, label='标签ID')

        # 4. 置信度图
        ax4 = axes[1, 1]
        im4 = ax4.imshow(confidence_image, cmap='viridis', alpha=0.8)
        ax4.set_title('标注置信度分布', fontsize=14)
        ax4.set_xlabel('像素 X (东西方向)')
        ax4.set_ylabel('像素 Y (南北方向)')
        plt.colorbar(im4, ax=ax4, label='置信度')

        plt.tight_layout()
        plt.savefig('georeferenced_labels_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 可视化完成！")

    def save_georeferenced_outputs(self, label_image, color_label_image, confidence_image):
        """保存地理坐标输出文件"""
        print("💾 正在保存地理坐标输出文件...")

        # 调试信息
        print(f"🔍 标签图像形状: {label_image.shape}")
        print(f"🔍 彩色标签图像形状: {color_label_image.shape}")
        print(f"🔍 置信度图像形状: {confidence_image.shape}")
        print(f"🔍 标签图像中非零像素数: {np.sum(label_image > 0)}")
        print(f"🔍 标签图像唯一值: {np.unique(label_image)}")

        # 1. 保存标签ID图像
        success1 = cv2.imwrite('georeferenced_label_ids.png', label_image)
        print(f"📁 标签ID图像保存: {'成功' if success1 else '失败'}")

        # 2. 保存彩色标签图像
        success2 = cv2.imwrite('georeferenced_color_labels.png', cv2.cvtColor(color_label_image, cv2.COLOR_RGB2BGR))
        print(f"📁 彩色标签图像保存: {'成功' if success2 else '失败'}")

        # 3. 保存置信度图像
        confidence_uint8 = (confidence_image * 255).astype(np.uint8)
        success3 = cv2.imwrite('georeferenced_confidence.png', confidence_uint8)
        print(f"📁 置信度图像保存: {'成功' if success3 else '失败'}")

        # 4. 保存地理信息
        geo_info = {
            'system_info': {
                'name': '地理坐标标签影像生成系统',
                'version': '1.0',
                'description': '将DJI影像标注映射到地理坐标系统生成标签影像',
                'timestamp': datetime.now().isoformat()
            },
            'geographic_info': {
                'bounds': {
                    'west': self.dom_bounds[0],
                    'south': self.dom_bounds[1],
                    'east': self.dom_bounds[2],
                    'north': self.dom_bounds[3]
                },
                'resolution_degrees': self.dom_resolution,
                'resolution_meters': self.dom_resolution * 111000,
                'size': {
                    'width': self.dom_size[0],
                    'height': self.dom_size[1]
                },
                'coordinate_system': 'WGS84 (EPSG:4326)'
            },
            'processing_stats': {
                'total_images': len(self.image_metadata),
                'annotated_images': len(self.annotations),
                'total_annotations': sum(len(annotations) for annotations in self.annotations.values()),
                'label_coverage_percent': (np.sum(label_image > 0) / (self.dom_size[0] * self.dom_size[1])) * 100,
                'unique_labels': len(np.unique(label_image[label_image > 0]))
            },
            'label_mapping': self.label_mapping,
            'output_files': [
                'georeferenced_label_ids.png',
                'georeferenced_color_labels.png',
                'georeferenced_confidence.png',
                'georeferenced_labels_visualization.png',
                'georeferenced_labels_info.json'
            ]
        }

        with open('georeferenced_labels_info.json', 'w', encoding='utf-8') as f:
            json.dump(geo_info, f, indent=2, ensure_ascii=False)

        print("✅ 地理坐标输出文件保存完成！")
        return geo_info

    def run_complete_system(self):
        """运行完整的地理坐标标签生成系统"""
        print("=" * 70)
        print("🚁 DJI影像标注 → 地理坐标标签影像生成系统")
        print("=" * 70)

        # 1. 加载数据
        self.load_image_metadata()
        self.load_annotations()

        if not self.image_metadata:
            print("❌ 没有找到有效的图像数据")
            return

        if not self.annotations:
            print("❌ 没有找到标注数据")
            return

        # 2. 计算DOM边界和创建网格
        self.calculate_dom_bounds()
        self.create_dom_grid(target_resolution_cm=30)  # 30cm分辨率

        # 3. 生成地理坐标标签影像
        label_image, color_label_image, confidence_image = self.generate_georeferenced_labels()

        # 4. 创建可视化
        self.create_comprehensive_visualization(label_image, color_label_image, confidence_image)

        # 5. 保存输出文件
        geo_info = self.save_georeferenced_outputs(label_image, color_label_image, confidence_image)

        # 6. 输出结果摘要
        print("\n" + "=" * 70)
        print("🎯 地理坐标标签影像生成完成！")
        print("=" * 70)
        print(f"📸 处理图像: {geo_info['processing_stats']['total_images']} 张")
        print(f"🏷️ 处理标注: {geo_info['processing_stats']['total_annotations']} 个")
        print(f"🗺️ 影像尺寸: {geo_info['geographic_info']['size']['width']} × {geo_info['geographic_info']['size']['height']} 像素")
        print(f"📐 分辨率: {geo_info['geographic_info']['resolution_meters'] * 100:.1f} cm/像素")
        print(f"📊 标签覆盖率: {geo_info['processing_stats']['label_coverage_percent']:.2f}%")
        print(f"🏷️ 标签类别数: {geo_info['processing_stats']['unique_labels']} 种")

        print(f"\n🌍 地理范围:")
        bounds = geo_info['geographic_info']['bounds']
        print(f"   经度: {bounds['west']:.6f}° - {bounds['east']:.6f}°")
        print(f"   纬度: {bounds['south']:.6f}° - {bounds['north']:.6f}°")

        print("\n📁 生成的文件:")
        for file in geo_info['output_files']:
            print(f"   • {file}")

        print("\n✅ 成功生成具有地理坐标的标签影像！")
        print("🌍 现在可以在GIS软件中使用这些带坐标的标签影像了！")

def main():
    """主函数"""
    generator = GeoreferencedLabelGenerator()
    generator.run_complete_system()

if __name__ == "__main__":
    main()
