#!/usr/bin/env python3
"""
完整的标注反投影到正射DOM系统
将原始DJI影像上的标注精确映射到地理坐标系统
"""

import json
import numpy as np
import cv2
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import os
import math
from collections import defaultdict
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
import matplotlib.patches as mpatches

class CompleteAnnotationReprojectionSystem:
    def __init__(self, image_dir=".", label_file="label.json"):
        self.image_dir = image_dir
        self.label_file = label_file
        self.image_metadata = {}
        self.annotations = {}
        self.dom_bounds = None
        self.dom_resolution = None
        self.dom_size = None
        self.voting_grid = None
        self.confidence_grid = None
        self.label_mapping = {
            'building': 1,
            'False Positive-Confusing (1)': 1,  # 统一为建筑物
            'house': 1,
            'structure': 1
        }
        
    def extract_gps_from_exif(self, exif_data):
        """从EXIF数据中提取GPS信息"""
        gps_info = {}
        
        gps_data = None
        for tag_id, value in exif_data.items():
            if tag_id == 'GPSInfo' or tag_id == 34853:
                gps_data = value
                break
        
        if gps_data:
            # 提取纬度
            if 2 in gps_data and 1 in gps_data:
                lat_dms = gps_data[2]
                lat_ref = gps_data[1]
                lat = self.dms_to_decimal(lat_dms)
                if lat_ref == 'S':
                    lat = -lat
                gps_info['latitude'] = lat
            
            # 提取经度
            if 4 in gps_data and 3 in gps_data:
                lon_dms = gps_data[4]
                lon_ref = gps_data[3]
                lon = self.dms_to_decimal(lon_dms)
                if lon_ref == 'W':
                    lon = -lon
                gps_info['longitude'] = lon
            
            # 提取高度
            if 6 in gps_data:
                gps_info['altitude'] = float(gps_data[6])
        
        return gps_info
    
    def dms_to_decimal(self, dms):
        """将度分秒转换为十进制度"""
        degrees, minutes, seconds = dms
        return float(degrees) + float(minutes)/60 + float(seconds)/3600
    
    def load_image_metadata(self):
        """加载所有图像的元数据"""
        print("🔍 正在加载图像元数据...")
        
        for filename in os.listdir(self.image_dir):
            if filename.lower().endswith(('.jpg', '.jpeg')) and filename.startswith('DJI_'):
                filepath = os.path.join(self.image_dir, filename)
                
                try:
                    with Image.open(filepath) as img:
                        exif_data = img._getexif()
                        if exif_data:
                            gps_info = self.extract_gps_from_exif(exif_data)
                            
                            focal_length = exif_data.get(37386, 12.29)
                            if isinstance(focal_length, tuple):
                                focal_length = focal_length[0] / focal_length[1]
                            
                            width, height = img.size
                            
                            self.image_metadata[filename] = {
                                'gps': gps_info,
                                'focal_length': focal_length,
                                'width': width,
                                'height': height,
                                'filepath': filepath
                            }
                            
                            if gps_info:
                                print(f"✅ {filename}: GPS({gps_info['latitude']:.6f}, {gps_info['longitude']:.6f})")
                        
                except Exception as e:
                    print(f"❌ 处理 {filename} 时出错: {e}")
        
        print(f"📊 成功加载 {len(self.image_metadata)} 张图像的元数据")
    
    def load_annotations(self):
        """加载标注数据（Label Studio格式）"""
        print("🏷️ 正在加载标注数据...")
        
        if not os.path.exists(self.label_file):
            print(f"❌ 标注文件 {self.label_file} 不存在")
            return
        
        with open(self.label_file, 'r', encoding='utf-8') as f:
            label_data = json.load(f)
        
        for item in label_data:
            image_path = item['data']['image']
            if '?' in image_path:
                image_name = image_path.split('=')[-1].replace('%5C', '/')
                image_name = os.path.basename(image_name)
            else:
                image_name = os.path.basename(image_path)
            
            if image_name in self.image_metadata:
                annotations = []
                
                for annotation_group in item.get('annotations', []):
                    for result in annotation_group.get('result', []):
                        if result['type'] == 'polygonlabels':
                            points_data = result['value']['points']
                            img_width = result['original_width']
                            img_height = result['original_height']
                            
                            points = []
                            for point in points_data:
                                x = point[0] * img_width / 100.0
                                y = point[1] * img_height / 100.0
                                points.append((x, y))
                            
                            labels = result['value'].get('polygonlabels', ['building'])
                            label = labels[0] if labels else 'building'
                            
                            annotations.append({
                                'type': 'polygon',
                                'points': points,
                                'label': label
                            })
                
                if annotations:
                    self.annotations[image_name] = annotations
                    print(f"📝 {image_name}: {len(annotations)} 个标注")
        
        print(f"📋 成功加载 {len(self.annotations)} 张图像的标注数据")
    
    def calculate_dom_bounds(self):
        """计算DOM影像的地理边界"""
        print("🗺️ 正在计算DOM地理边界...")
        
        lats = []
        lons = []
        
        for metadata in self.image_metadata.values():
            gps = metadata.get('gps', {})
            if 'latitude' in gps and 'longitude' in gps:
                lats.append(gps['latitude'])
                lons.append(gps['longitude'])
        
        if not lats or not lons:
            print("❌ 没有找到有效的GPS数据")
            return
        
        min_lat, max_lat = min(lats), max(lats)
        min_lon, max_lon = min(lons), max(lons)
        
        # 计算覆盖范围并添加缓冲区
        lat_range = max_lat - min_lat
        lon_range = max_lon - min_lon
        
        # 根据飞行高度估算覆盖范围
        avg_altitude = np.mean([m['gps'].get('altitude', 100) for m in self.image_metadata.values() 
                               if 'gps' in m and 'altitude' in m['gps']])
        
        # 估算单张影像的地面覆盖范围（米）
        focal_length = 12.29  # DJI M3E
        sensor_width = 23.5   # mm
        ground_coverage = (avg_altitude * sensor_width) / focal_length  # 米
        
        # 转换为度数缓冲区
        meters_per_degree = 111000
        buffer_degrees = (ground_coverage / meters_per_degree) * 0.5
        
        self.dom_bounds = (
            min_lon - buffer_degrees,  # west
            min_lat - buffer_degrees,  # south
            max_lon + buffer_degrees,  # east
            max_lat + buffer_degrees   # north
        )
        
        print(f"🌍 DOM边界: 经度[{self.dom_bounds[0]:.6f}, {self.dom_bounds[2]:.6f}], "
              f"纬度[{self.dom_bounds[1]:.6f}, {self.dom_bounds[3]:.6f}]")
        print(f"📏 覆盖范围: {lon_range*111000:.1f}m × {lat_range*111000:.1f}m")
    
    def create_dom_grid(self, target_resolution_cm=10):
        """创建DOM网格（指定分辨率）"""
        if not self.dom_bounds:
            self.calculate_dom_bounds()
        
        west, south, east, north = self.dom_bounds
        
        # 根据目标分辨率计算网格大小
        meters_per_degree_lat = 111000
        meters_per_degree_lon = 111000 * math.cos(math.radians((north + south) / 2))
        
        resolution_meters = target_resolution_cm / 100.0
        self.dom_resolution = resolution_meters / min(meters_per_degree_lat, meters_per_degree_lon)
        
        lon_range = east - west
        lat_range = north - south
        
        self.dom_size = (
            int(lon_range / self.dom_resolution),
            int(lat_range / self.dom_resolution)
        )
        
        # 限制最大尺寸
        max_size = 5000
        if max(self.dom_size) > max_size:
            scale = max_size / max(self.dom_size)
            self.dom_size = (int(self.dom_size[0] * scale), int(self.dom_size[1] * scale))
            self.dom_resolution = self.dom_resolution / scale
        
        # 初始化投票网格
        self.voting_grid = np.zeros((*self.dom_size[::-1], 5), dtype=np.int32)  # 支持5个类别
        self.confidence_grid = np.zeros(self.dom_size[::-1], dtype=np.float32)
        
        actual_resolution_cm = self.dom_resolution * min(meters_per_degree_lat, meters_per_degree_lon) * 100
        print(f"🎯 DOM网格: {self.dom_size[0]} × {self.dom_size[1]} 像素")
        print(f"📐 实际分辨率: {actual_resolution_cm:.1f} cm/像素")
    
    def geo_to_pixel(self, lon, lat):
        """地理坐标转像素坐标"""
        if not self.dom_bounds or not self.dom_resolution:
            return None, None
        
        west, south, east, north = self.dom_bounds
        
        x = int((lon - west) / self.dom_resolution)
        y = int((north - lat) / self.dom_resolution)
        
        return x, y
    
    def pixel_to_geo(self, x, y):
        """像素坐标转地理坐标"""
        if not self.dom_bounds or not self.dom_resolution:
            return None, None
        
        west, south, east, north = self.dom_bounds
        
        lon = west + x * self.dom_resolution
        lat = north - y * self.dom_resolution
        
        return lon, lat
    
    def precise_image_pixel_to_geo(self, image_name, img_x, img_y):
        """精确的图像像素坐标转地理坐标"""
        if image_name not in self.image_metadata:
            return None, None

        metadata = self.image_metadata[image_name]
        gps = metadata.get('gps', {})

        if 'latitude' not in gps or 'longitude' not in gps:
            return None, None

        center_lat = gps['latitude']
        center_lon = gps['longitude']
        altitude = gps.get('altitude', 100)
        focal_length = metadata['focal_length']
        img_width = metadata['width']
        img_height = metadata['height']

        # DJI M3E传感器参数
        sensor_width = 23.5   # mm
        sensor_height = 15.6  # mm

        # 计算地面采样距离（GSD）
        gsd_x = (altitude * sensor_width) / (focal_length * img_width) / 1000  # 米/像素
        gsd_y = (altitude * sensor_height) / (focal_length * img_height) / 1000

        # 转换为度
        meters_per_degree_lat = 111000
        meters_per_degree_lon = 111000 * math.cos(math.radians(center_lat))

        degrees_per_pixel_x = gsd_x / meters_per_degree_lon
        degrees_per_pixel_y = gsd_y / meters_per_degree_lat

        # 计算相对于图像中心的偏移
        dx_pixels = img_x - img_width / 2
        dy_pixels = img_y - img_height / 2

        # 转换为地理坐标
        lon = center_lon + dx_pixels * degrees_per_pixel_x
        lat = center_lat - dy_pixels * degrees_per_pixel_y

        return lon, lat

    def reproject_annotations_with_voting(self):
        """使用投票机制进行标注反投影"""
        print("🔄 正在反投影标注到DOM...")

        if not self.dom_size:
            self.create_dom_grid()

        annotation_count = 0

        # 创建标签颜色映射
        label_colors = {
            1: (255, 0, 0),    # 建筑物 - 红色
            2: (0, 255, 0),    # 道路 - 绿色
            3: (0, 0, 255),    # 水体 - 蓝色
            4: (255, 255, 0)   # 其他 - 黄色
        }

        # 创建DOM标签图像
        dom_labels = np.zeros((*self.dom_size[::-1], 3), dtype=np.uint8)

        # 创建投票计数图像
        vote_count_image = np.zeros(self.dom_size[::-1], dtype=np.uint8)

        for image_name, annotations in self.annotations.items():
            print(f"🖼️ 处理图像: {image_name}")

            for annotation in annotations:
                if annotation['type'] == 'polygon':
                    geo_points = []
                    for img_x, img_y in annotation['points']:
                        lon, lat = self.precise_image_pixel_to_geo(image_name, img_x, img_y)
                        if lon is not None and lat is not None:
                            dom_x, dom_y = self.geo_to_pixel(lon, lat)
                            if (0 <= dom_x < self.dom_size[0] and
                                0 <= dom_y < self.dom_size[1]):
                                geo_points.append([dom_x, dom_y])

                    if len(geo_points) >= 3:
                        poly_array = np.array(geo_points, dtype=np.int32)

                        # 创建掩码
                        mask = np.zeros(self.dom_size[::-1], dtype=np.uint8)
                        cv2.fillPoly(mask, [poly_array], 1)

                        # 获取标签ID
                        label = annotation['label']
                        label_id = self.label_mapping.get(label, 1)

                        # 投票
                        self.voting_grid[:, :, label_id] += mask

                        # 更新置信度
                        area = cv2.contourArea(poly_array)
                        confidence = min(1.0, area / 1000.0)
                        self.confidence_grid += mask * confidence

                        annotation_count += 1

        # 生成最终分割结果
        final_labels = np.argmax(self.voting_grid, axis=2).astype(np.uint8)

        # 生成彩色标签图像
        for label_id, color in label_colors.items():
            mask = (final_labels == label_id)
            dom_labels[mask] = color

        # 生成投票计数图像
        for i in range(1, 5):  # 假设有4个类别
            vote_count_image += (self.voting_grid[:, :, i] > 0).astype(np.uint8)

        # 缩放到0-255
        vote_count_image = np.minimum(vote_count_image * 50, 255).astype(np.uint8)

        print(f"✅ 成功反投影 {annotation_count} 个标注")

        return dom_labels, vote_count_image, final_labels

    def create_comprehensive_visualization(self, dom_labels, vote_count_image, final_labels):
        """创建综合可视化"""
        print("🎨 正在创建可视化...")

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('DJI影像标注反投影到正射DOM系统', fontsize=16, fontweight='bold')

        # 1. 原始DOM区域（灰度背景）
        ax1 = axes[0, 0]
        background = np.ones(self.dom_size[::-1], dtype=np.uint8) * 128
        ax1.imshow(background, cmap='gray', alpha=0.5)
        ax1.set_title('DOM覆盖区域', fontsize=14)
        ax1.set_xlabel('像素 X')
        ax1.set_ylabel('像素 Y')

        # 添加图像中心点
        for image_name, metadata in self.image_metadata.items():
            gps = metadata.get('gps', {})
            if 'latitude' in gps and 'longitude' in gps:
                dom_x, dom_y = self.geo_to_pixel(gps['longitude'], gps['latitude'])
                if (0 <= dom_x < self.dom_size[0] and 0 <= dom_y < self.dom_size[1]):
                    ax1.plot(dom_x, dom_y, 'ro', markersize=3, alpha=0.7)

        # 2. 标注反投影结果（彩色）
        ax2 = axes[0, 1]
        ax2.imshow(dom_labels)
        ax2.set_title('标注反投影结果', fontsize=14)
        ax2.set_xlabel('像素 X')
        ax2.set_ylabel('像素 Y')

        # 添加图例
        legend_elements = [
            mpatches.Patch(color='red', label='建筑物'),
            mpatches.Patch(color='green', label='道路'),
            mpatches.Patch(color='blue', label='水体'),
            mpatches.Patch(color='yellow', label='其他')
        ]
        ax2.legend(handles=legend_elements, loc='upper right')

        # 3. 投票密度图
        ax3 = axes[1, 0]
        im3 = ax3.imshow(vote_count_image, cmap='hot', alpha=0.8)
        ax3.set_title('标注投票密度', fontsize=14)
        ax3.set_xlabel('像素 X')
        ax3.set_ylabel('像素 Y')
        plt.colorbar(im3, ax=ax3, label='投票次数')

        # 4. 置信度图
        ax4 = axes[1, 1]
        im4 = ax4.imshow(self.confidence_grid, cmap='viridis', alpha=0.8)
        ax4.set_title('标注置信度', fontsize=14)
        ax4.set_xlabel('像素 X')
        ax4.set_ylabel('像素 Y')
        plt.colorbar(im4, ax=ax4, label='置信度')

        plt.tight_layout()
        plt.savefig('complete_dom_reprojection_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 创建叠加可视化
        self.create_overlay_visualization(dom_labels)

        print("✅ 可视化完成！")

    def create_overlay_visualization(self, dom_labels):
        """创建叠加可视化（模拟正射影像效果）"""
        print("🌍 正在创建正射影像叠加效果...")

        # 创建模拟的正射影像背景
        background = self.create_simulated_orthophoto()

        # 创建透明的标注叠加层
        overlay = np.zeros((*self.dom_size[::-1], 4), dtype=np.uint8)

        # 添加建筑物标注（红色，半透明）
        building_mask = np.any(dom_labels == [255, 0, 0], axis=2)
        overlay[building_mask] = [255, 0, 0, 150]  # 红色，透明度150/255

        # 保存叠加结果
        fig, ax = plt.subplots(1, 1, figsize=(15, 12))
        ax.imshow(background, cmap='gray')
        ax.imshow(overlay, alpha=0.6)
        ax.set_title('标注叠加到正射DOM效果图', fontsize=16, fontweight='bold')
        ax.set_xlabel('像素 X (地理坐标系)')
        ax.set_ylabel('像素 Y (地理坐标系)')

        # 添加比例尺
        scale_length_pixels = int(100 / (self.dom_resolution * 111000))  # 100米
        ax.plot([50, 50 + scale_length_pixels], [self.dom_size[1] - 50, self.dom_size[1] - 50],
                'k-', linewidth=3)
        ax.text(50, self.dom_size[1] - 70, '100m', fontsize=12, fontweight='bold')

        # 添加北向箭头
        ax.annotate('N', xy=(self.dom_size[0] - 50, 50), xytext=(self.dom_size[0] - 50, 100),
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                   fontsize=14, fontweight='bold', ha='center')

        plt.tight_layout()
        plt.savefig('orthophoto_annotation_overlay.png', dpi=300, bbox_inches='tight')
        plt.close()

    def create_simulated_orthophoto(self):
        """创建模拟的正射影像背景"""
        # 创建基础地形纹理
        np.random.seed(42)  # 确保可重复性

        # 生成地形高度图
        terrain = np.random.rand(*self.dom_size[::-1]) * 0.3 + 0.4

        # 添加一些结构化特征（模拟道路、建筑等）
        for i in range(0, self.dom_size[1], 100):
            terrain[i:i+5, :] *= 0.7  # 水平道路

        for j in range(0, self.dom_size[0], 150):
            terrain[:, j:j+5] *= 0.7  # 垂直道路

        # 转换为0-255灰度值
        background = (terrain * 255).astype(np.uint8)

        return background

    def generate_geotiff_output(self, final_labels):
        """生成GeoTIFF格式的地理标注文件"""
        try:
            from osgeo import gdal, osr

            print("🌍 正在生成GeoTIFF文件...")

            # 创建GeoTIFF文件
            driver = gdal.GetDriverByName('GTiff')
            dataset = driver.Create('dom_annotations.tif',
                                  self.dom_size[0], self.dom_size[1], 1, gdal.GDT_Byte)

            # 设置地理变换参数
            west, south, east, north = self.dom_bounds
            geotransform = [
                west,                    # 左上角X坐标
                self.dom_resolution,     # X方向像素分辨率
                0,                       # X方向旋转
                north,                   # 左上角Y坐标
                0,                       # Y方向旋转
                -self.dom_resolution     # Y方向像素分辨率（负值）
            ]
            dataset.SetGeoTransform(geotransform)

            # 设置坐标系（WGS84）
            srs = osr.SpatialReference()
            srs.ImportFromEPSG(4326)
            dataset.SetProjection(srs.ExportToWkt())

            # 写入数据
            band = dataset.GetRasterBand(1)
            band.WriteArray(final_labels)
            band.SetNoDataValue(0)

            # 设置颜色表
            color_table = gdal.ColorTable()
            color_table.SetColorEntry(0, (0, 0, 0, 0))      # 背景 - 透明
            color_table.SetColorEntry(1, (255, 0, 0, 255))  # 建筑物 - 红色
            color_table.SetColorEntry(2, (0, 255, 0, 255))  # 道路 - 绿色
            color_table.SetColorEntry(3, (0, 0, 255, 255))  # 水体 - 蓝色
            color_table.SetColorEntry(4, (255, 255, 0, 255)) # 其他 - 黄色
            band.SetColorTable(color_table)

            dataset = None  # 关闭文件
            print("✅ GeoTIFF文件已生成: dom_annotations.tif")

        except ImportError:
            print("⚠️ GDAL未安装，跳过GeoTIFF生成")

    def save_statistics(self, annotation_count):
        """保存统计信息"""
        stats = {
            'system_info': {
                'name': '完整DJI影像标注反投影系统',
                'version': '1.0',
                'description': '将原始DJI影像标注精确映射到地理坐标系统'
            },
            'processing_stats': {
                'total_images': len(self.image_metadata),
                'annotated_images': len(self.annotations),
                'total_annotations': annotation_count,
                'dom_size': self.dom_size,
                'dom_resolution_degrees': self.dom_resolution,
                'dom_bounds': self.dom_bounds
            },
            'coverage_analysis': {
                'annotation_coverage_percent': (np.sum(np.any(self.voting_grid > 0, axis=2)) /
                                               (self.dom_size[0] * self.dom_size[1])) * 100,
                'max_vote_count': int(np.max(self.voting_grid)),
                'average_confidence': float(np.mean(self.confidence_grid[self.confidence_grid > 0]))
            },
            'output_files': [
                'complete_dom_reprojection_visualization.png',
                'orthophoto_annotation_overlay.png',
                'dom_annotations.tif',
                'complete_reprojection_stats.json'
            ]
        }

        with open('complete_reprojection_stats.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        print("📊 统计信息已保存")
        return stats

    def run_complete_system(self):
        """运行完整的标注反投影系统"""
        print("=" * 60)
        print("🚁 完整DJI影像标注反投影到正射DOM系统")
        print("=" * 60)

        # 1. 加载数据
        self.load_image_metadata()
        self.load_annotations()

        if not self.image_metadata:
            print("❌ 没有找到有效的图像数据")
            return

        if not self.annotations:
            print("❌ 没有找到标注数据")
            return

        # 2. 创建DOM网格
        self.create_dom_grid(target_resolution_cm=10)  # 10cm分辨率

        # 3. 反投影标注
        dom_labels, vote_count_image, final_labels = self.reproject_annotations_with_voting()

        # 4. 创建可视化
        self.create_comprehensive_visualization(dom_labels, vote_count_image, final_labels)

        # 5. 生成GeoTIFF
        self.generate_geotiff_output(final_labels)

        # 6. 保存统计信息
        annotation_count = sum(len(annotations) for annotations in self.annotations.values())
        stats = self.save_statistics(annotation_count)

        # 7. 输出结果摘要
        print("\n" + "=" * 60)
        print("🎯 处理完成！结果摘要:")
        print("=" * 60)
        print(f"📸 处理图像: {stats['processing_stats']['total_images']} 张")
        print(f"🏷️ 反投影标注: {stats['processing_stats']['total_annotations']} 个")
        print(f"🗺️ DOM尺寸: {stats['processing_stats']['dom_size'][0]} × {stats['processing_stats']['dom_size'][1]} 像素")
        print(f"📐 分辨率: {self.dom_resolution * 111000 * 100:.1f} cm/像素")
        print(f"📊 标注覆盖率: {stats['coverage_analysis']['annotation_coverage_percent']:.2f}%")
        print(f"🎯 最大投票数: {stats['coverage_analysis']['max_vote_count']}")
        print(f"💯 平均置信度: {stats['coverage_analysis']['average_confidence']:.3f}")

        print("\n📁 输出文件:")
        for file in stats['output_files']:
            print(f"   • {file}")

        print("\n✅ 标注已成功反投影到地理坐标系统！")
        print("🌍 现在可以进行精确的地理分析和监测了！")

def main():
    """主函数"""
    system = CompleteAnnotationReprojectionSystem()
    system.run_complete_system()

if __name__ == "__main__":
    main()
