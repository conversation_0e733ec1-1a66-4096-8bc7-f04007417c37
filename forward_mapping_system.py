#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正向映射系统 - 按照需求.png中的流程实现正向映射
"""

import os
import json
import numpy as np
import cv2
import urllib.parse
import re
from datetime import datetime

class ForwardMappingSystem:
    def __init__(self):
        self.original_images = {}      # 原始影像
        self.rasterized_labels = {}    # 栅格化标签
        self.geographic_mapping = {}   # 地理对应关系
        self.dom_segmentation = None   # DOM分割结果
        
    def step1_load_original_images(self):
        """步骤1: 加载原始影像"""
        print("🖼️ 步骤1: 加载原始影像...")
        
        # 加载JSON标签数据
        label_file = 'label.json'
        if not os.path.exists(label_file):
            print("❌ label.json文件不存在")
            return False
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            
            for task in label_data:
                if 'data' in task and 'image' in task['data']:
                    image_path = task['data']['image']
                    # 提取干净的文件名
                    clean_name = self.extract_clean_filename(image_path)
                    
                    if 'annotations' in task and task['annotations']:
                        annotation = task['annotations'][0]
                        if 'result' in annotation:
                            polygons = []
                            for result in annotation['result']:
                                if 'value' in result and 'points' in result['value']:
                                    polygons.append({
                                        'points': result['value']['points'],
                                        'labels': result['value'].get('polygonlabels', ['house'])
                                    })
                            
                            if polygons:
                                self.original_images[clean_name] = {
                                    'polygons': polygons,
                                    'original_path': image_path,
                                    'image_size': (5280, 3956)  # 从之前的分析得知
                                }
            
            print(f"   ✅ 加载了 {len(self.original_images)} 个原始影像的标注信息")
            return True
            
        except Exception as e:
            print(f"❌ 加载原始影像失败: {e}")
            return False
    
    def extract_clean_filename(self, image_path):
        """提取干净的文件名"""
        decoded_path = urllib.parse.unquote(image_path)
        
        if '\\' in decoded_path:
            filename = decoded_path.split('\\')[-1]
        elif '/' in decoded_path:
            filename = decoded_path.split('/')[-1]
        else:
            filename = decoded_path
        
        # 移除可能的前缀
        if filename.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
            filename = filename[2:]
        
        return filename
    
    def step2_rasterize_labels(self):
        """步骤2: 栅格化标签"""
        print("🎯 步骤2: 栅格化标签...")
        
        for image_name, image_data in self.original_images.items():
            print(f"   🎯 栅格化: {image_name}")
            
            # 创建空白掩码
            width, height = image_data['image_size']
            mask = np.zeros((height, width), dtype=np.uint8)
            
            # 栅格化每个多边形
            for polygon in image_data['polygons']:
                points = polygon['points']
                if len(points) >= 3:
                    # 转换为像素坐标
                    pixel_points = []
                    for point in points:
                        x = int(point[0] * width / 100.0)  # 百分比转像素
                        y = int(point[1] * height / 100.0)
                        pixel_points.append([x, y])
                    
                    # 填充多边形
                    cv2.fillPoly(mask, [np.array(pixel_points, dtype=np.int32)], 1)
            
            # 统计房子像素
            house_pixels = np.sum(mask == 1)
            house_percentage = (house_pixels / (width * height)) * 100
            
            self.rasterized_labels[image_name] = {
                'mask': mask,
                'house_pixels': house_pixels,
                'house_percentage': house_percentage,
                'width': width,
                'height': height
            }
            
            print(f"      ✅ 房子像素: {house_pixels:,} ({house_percentage:.2f}%)")
        
        print(f"   📊 栅格化完成: {len(self.rasterized_labels)} 个标签掩码")
        return True
    
    def step3_establish_geographic_correspondence(self):
        """步骤3: 建立地理对应关系"""
        print("🗺️ 步骤3: 建立地理对应关系...")
        
        # 从文件名提取时间信息，建立飞行序列
        image_sequence = []
        for image_name in self.rasterized_labels.keys():
            time_match = re.search(r'(\d{8})(\d{6})', image_name)
            seq_match = re.search(r'_(\d+)_?V?\.JPG', image_name)
            
            if time_match and seq_match:
                date_str, time_str = time_match.groups()
                seq_num = int(seq_match.group(1))
                
                try:
                    dt = datetime.strptime(f"{date_str}{time_str}", "%Y%m%d%H%M%S")
                    timestamp = dt.timestamp()
                    
                    image_sequence.append({
                        'name': image_name,
                        'timestamp': timestamp,
                        'sequence': seq_num,
                        'datetime': dt
                    })
                except:
                    pass
        
        # 按时间排序
        image_sequence.sort(key=lambda x: x['timestamp'])
        
        print(f"   ⏰ 建立了 {len(image_sequence)} 个图像的时间序列")
        
        # 分析多边形的空间分布，建立地理对应关系
        for i, img_info in enumerate(image_sequence):
            image_name = img_info['name']
            
            if image_name in self.original_images:
                image_data = self.original_images[image_name]
                
                # 计算多边形重心
                centroids = []
                for polygon in image_data['polygons']:
                    points = polygon['points']
                    if len(points) >= 3:
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        centroid_x = sum(x_coords) / len(x_coords)
                        centroid_y = sum(y_coords) / len(y_coords)
                        centroids.append((centroid_x, centroid_y))
                
                if centroids:
                    # 计算平均重心
                    avg_x = sum(c[0] for c in centroids) / len(centroids)
                    avg_y = sum(c[1] for c in centroids) / len(centroids)
                    
                    # 建立地理对应关系
                    self.geographic_mapping[image_name] = {
                        'flight_order': i,
                        'centroid_x': avg_x,
                        'centroid_y': avg_y,
                        'num_polygons': len(centroids),
                        'timestamp': img_info['timestamp'],
                        'datetime': img_info['datetime']
                    }
                    
                    print(f"   📍 {i+1:2d}. {image_name}: 重心({avg_x:.1f}, {avg_y:.1f})")
        
        print(f"   ✅ 地理对应关系建立完成: {len(self.geographic_mapping)} 个映射")
        return True
    
    def step4_create_dom_segmentation(self):
        """步骤4: 创建DOM分割结果"""
        print("🎨 步骤4: 创建DOM分割结果...")
        
        # 创建DOM画布
        dom_size = 4000  # 4000x4000的高分辨率DOM
        self.dom_segmentation = np.zeros((dom_size, dom_size), dtype=np.uint8)
        
        # 分析空间分布范围
        x_coords = [mapping['centroid_x'] for mapping in self.geographic_mapping.values()]
        y_coords = [mapping['centroid_y'] for mapping in self.geographic_mapping.values()]
        
        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)
        
        print(f"   📐 空间范围: X({min_x:.1f}-{max_x:.1f}), Y({min_y:.1f}-{max_y:.1f})")
        
        # 计算映射参数
        x_range = max_x - min_x if max_x > min_x else 100
        y_range = max_y - min_y if max_y > min_y else 100
        
        # 留出边界
        margin = 0.1
        effective_size = dom_size * (1 - 2 * margin)
        
        total_mapped_pixels = 0
        
        for image_name, mapping in self.geographic_mapping.items():
            if image_name in self.rasterized_labels:
                print(f"   🎯 映射: {image_name}")
                
                # 计算在DOM中的位置
                norm_x = (mapping['centroid_x'] - min_x) / x_range
                norm_y = (mapping['centroid_y'] - min_y) / y_range
                
                # 映射到DOM坐标
                dom_x = int(margin * dom_size + norm_x * effective_size)
                dom_y = int(margin * dom_size + norm_y * effective_size)
                
                # 计算图像覆盖区域
                coverage_size = 200  # 每个图像覆盖200x200像素
                
                start_x = max(0, dom_x - coverage_size // 2)
                end_x = min(dom_size, start_x + coverage_size)
                start_y = max(0, dom_y - coverage_size // 2)
                end_y = min(dom_size, start_y + coverage_size)
                
                # 执行正向映射
                mapped_pixels = self.execute_forward_mapping(
                    image_name, start_y, end_y, start_x, end_x
                )
                
                total_mapped_pixels += mapped_pixels
                
                print(f"      ✅ DOM位置({dom_x},{dom_y}) -> 映射{mapped_pixels:,}像素")
        
        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_pixels = dom_size * dom_size
        
        print(f"\n   📊 DOM分割结果统计:")
        for label, count in zip(unique_labels, counts):
            label_name = 'Background' if label == 0 else 'House'
            percentage = (count / total_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        print(f"   🎯 总映射像素: {total_mapped_pixels:,}")
        return True
    
    def execute_forward_mapping(self, image_name, start_y, end_y, start_x, end_x):
        """执行正向映射"""
        raster_data = self.rasterized_labels[image_name]
        label_mask = raster_data['mask']
        
        actual_height = end_y - start_y
        actual_width = end_x - start_x
        
        if actual_height <= 0 or actual_width <= 0:
            return 0
        
        # 高质量缩放栅格化标签
        scaled_mask = cv2.resize(label_mask, (actual_width, actual_height), 
                               interpolation=cv2.INTER_AREA)
        
        # 确保二值化
        scaled_mask = (scaled_mask > 127).astype(np.uint8)
        
        # 获取DOM区域并直接应用
        dom_region = self.dom_segmentation[start_y:end_y, start_x:end_x]
        house_pixels = scaled_mask == 1
        dom_region[house_pixels] = 1
        
        return np.sum(house_pixels)
    
    def save_forward_mapping_results(self):
        """保存正向映射结果"""
        print("💾 保存正向映射结果...")
        
        # 保存DOM分割掩码
        cv2.imwrite('forward_mapping_dom_mask.png', self.dom_segmentation)
        
        # 创建彩色DOM分割结果
        dom_size = self.dom_segmentation.shape[0]
        segmentation_rgb = np.zeros((dom_size, dom_size, 3), dtype=np.uint8)
        
        # 使用标准颜色
        segmentation_rgb[self.dom_segmentation == 0] = [0, 0, 0]        # 黑色背景
        segmentation_rgb[self.dom_segmentation == 1] = [255, 255, 0]    # 黄色房子
        
        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('forward_mapping_dom_result.png', segmentation_bgr)
        
        # 创建多尺寸版本
        scales = [0.05, 0.1, 0.2, 0.5]
        for scale in scales:
            small_size = int(dom_size * scale)
            if small_size > 0:
                small_result = cv2.resize(segmentation_bgr, (small_size, small_size), 
                                        interpolation=cv2.INTER_NEAREST)
                cv2.imwrite(f'forward_mapping_dom_{int(scale*100)}percent.png', small_result)
                print(f"   ✅ {int(scale*100)}%版本: forward_mapping_dom_{int(scale*100)}percent.png ({small_size}x{small_size})")
        
        # 保存正向映射信息
        house_pixels = int(np.sum(self.dom_segmentation == 1))
        total_pixels = dom_size * dom_size
        
        mapping_info = {
            'forward_mapping_method': 'geographic_correspondence_based',
            'dom_size': dom_size,
            'num_original_images': len(self.original_images),
            'num_rasterized_labels': len(self.rasterized_labels),
            'num_geographic_mappings': len(self.geographic_mapping),
            'total_house_pixels': house_pixels,
            'house_percentage': float(house_pixels / total_pixels * 100),
            'mapping_success': house_pixels > 0
        }
        
        with open('forward_mapping_info.json', 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)
        
        print("   ✅ DOM分割结果已保存: forward_mapping_dom_result.png")
        print("   ✅ DOM分割掩码已保存: forward_mapping_dom_mask.png")
        print("   ✅ 映射信息已保存: forward_mapping_info.json")
        
        return True
    
    def run_forward_mapping(self):
        """运行正向映射系统"""
        print("=" * 80)
        print("➡️ 正向映射系统 - 按照需求.png流程实现")
        print("=" * 80)
        
        # 步骤1: 加载原始影像
        if not self.step1_load_original_images():
            print("❌ 步骤1失败")
            return False
        
        # 步骤2: 栅格化标签
        if not self.step2_rasterize_labels():
            print("❌ 步骤2失败")
            return False
        
        # 步骤3: 建立地理对应关系
        if not self.step3_establish_geographic_correspondence():
            print("❌ 步骤3失败")
            return False
        
        # 步骤4: 创建DOM分割结果
        if not self.step4_create_dom_segmentation():
            print("❌ 步骤4失败")
            return False
        
        # 保存结果
        if not self.save_forward_mapping_results():
            print("❌ 保存结果失败")
            return False
        
        print("\n" + "=" * 80)
        print("➡️ 正向映射完成！")
        print("=" * 80)
        print("📁 生成的正向映射文件:")
        print("   • forward_mapping_dom_result.png - DOM分割结果")
        print("   • forward_mapping_dom_5percent.png - 5%版本 ⭐推荐查看")
        print("   • forward_mapping_dom_10percent.png - 10%版本")
        print("   • forward_mapping_dom_20percent.png - 20%版本")
        print("   • forward_mapping_dom_50percent.png - 50%版本")
        print("   • forward_mapping_dom_mask.png - DOM分割掩码")
        print("   • forward_mapping_info.json - 映射信息")
        
        print("\n➡️ 正向映射流程:")
        print("   1️⃣ 原始影像 ✅")
        print("   2️⃣ 栅格化标签 ✅")
        print("   3️⃣ 地理对应关系 ✅")
        print("   4️⃣ DOM分割结果 ✅")
        
        return True

def main():
    """主函数"""
    system = ForwardMappingSystem()
    success = system.run_forward_mapping()
    
    if success:
        print("\n🎉 正向映射成功！")
        print("📋 完成的正向映射流程:")
        print("   ✅ 从原始影像开始")
        print("   ✅ 栅格化JSON标签")
        print("   ✅ 建立地理对应关系")
        print("   ✅ 生成DOM分割结果")
        print("\n💡 建议查看: forward_mapping_dom_5percent.png")
    else:
        print("\n❌ 正向映射失败")

if __name__ == "__main__":
    main()
