#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOM-原始影像对应关系分析系统
基于原始影像生成DOM，建立DOM像元与原始图像的对应关系并可视化
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
from PIL import Image, ExifTags
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
def setup_chinese_font():
    """设置中文字体"""
    import matplotlib.font_manager as fm

    # 尝试多种中文字体
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'DejaVu Sans']

    for font_name in chinese_fonts:
        try:
            # 检查字体是否可用
            font_list = [f.name for f in fm.fontManager.ttflist]
            if font_name in font_list:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"   🔤 使用中文字体: {font_name}")
                return font_name
        except:
            continue

    # 如果没有找到中文字体，使用默认设置
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("   ⚠️ 未找到中文字体，使用默认字体")
    return 'DejaVu Sans'

# 初始化字体
CHINESE_FONT = setup_chinese_font()

class DOMOriginalMappingSystem:
    def __init__(self):
        self.original_images = []
        self.dom_path = None
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.pixel_mapping = {}  # DOM像元到原始影像的映射关系
        self.coverage_stats = {}
        
    def load_original_images(self):
        """加载原始DJI影像信息"""
        print("🔍 正在加载原始DJI影像信息...")
        
        image_files = [f for f in os.listdir('.') if f.startswith('DJI_') and f.endswith('.JPG')]
        image_files.sort()
        
        for i, filename in enumerate(image_files):
            try:
                # 读取图像基本信息
                image = cv2.imread(filename)
                if image is not None:
                    height, width = image.shape[:2]
                    
                    # 尝试读取EXIF信息
                    exif_info = self.extract_exif_info(filename)
                    
                    image_info = {
                        'id': i + 1,
                        'filename': filename,
                        'width': width,
                        'height': height,
                        'exif': exif_info,
                        'bounds': self.estimate_image_bounds(filename, exif_info)
                    }
                    
                    self.original_images.append(image_info)
                    print(f"   ✅ {filename}: {width}x{height}")
                    
            except Exception as e:
                print(f"   ❌ 处理 {filename} 时出错: {e}")
        
        print(f"📸 成功加载 {len(self.original_images)} 张原始影像")
    
    def extract_exif_info(self, filename):
        """提取EXIF信息"""
        try:
            with Image.open(filename) as img:
                exif_dict = img._getexif()
                if exif_dict:
                    exif_info = {}
                    for tag_id, value in exif_dict.items():
                        tag = ExifTags.TAGS.get(tag_id, tag_id)
                        exif_info[tag] = value
                    return exif_info
        except:
            pass
        return {}
    
    def estimate_image_bounds(self, filename, exif_info):
        """估算影像地理边界（简化版本）"""
        # 这里使用简化的方法，实际项目中需要精确的地理定位
        # 基于文件名中的时间戳估算大致位置
        try:
            # 从文件名提取序号
            parts = filename.split('_')
            if len(parts) >= 3:
                seq_num = int(parts[2])
                
                # 模拟飞行路径（实际应该从GPS数据获取）
                base_lon = 120.0  # 基准经度
                base_lat = 30.0   # 基准纬度
                
                # 简单的网格飞行模式
                cols = 6  # 假设6列
                row = (seq_num - 1) // cols
                col = (seq_num - 1) % cols
                
                # 每张影像覆盖约0.001度
                img_size = 0.001
                
                min_lon = base_lon + col * img_size * 0.8  # 80%重叠
                max_lon = min_lon + img_size
                min_lat = base_lat + row * img_size * 0.8
                max_lat = min_lat + img_size
                
                return {
                    'min_lon': min_lon,
                    'max_lon': max_lon,
                    'min_lat': min_lat,
                    'max_lat': max_lat
                }
        except:
            pass
        
        return None
    
    def load_dom_data(self):
        """加载DOM数据"""
        print("🗺️ 正在加载DOM数据...")

        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False

        try:
            # 使用OpenCV读取DOM图像
            self.dom_data = cv2.imread(dom_file, cv2.IMREAD_UNCHANGED)
            if self.dom_data is None:
                print("❌ 无法读取DOM文件")
                return False

            self.dom_path = dom_file

            print(f"   ✅ DOM尺寸: {self.dom_data.shape}")
            print(f"   ✅ DOM文件路径: {dom_file}")

            return True

        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def calculate_pixel_mapping(self):
        """计算DOM像元与原始影像的对应关系"""
        print("🔗 正在计算DOM像元与原始影像的对应关系...")

        if self.dom_data is None:
            print("❌ DOM数据未加载")
            return

        # 获取DOM的尺寸
        if len(self.dom_data.shape) == 3:
            dom_height, dom_width = self.dom_data.shape[0], self.dom_data.shape[1]
        else:
            dom_height, dom_width = self.dom_data.shape[0], self.dom_data.shape[1]

        print(f"   📐 DOM尺寸: {dom_width} x {dom_height}")

        # 初始化映射矩阵
        mapping_matrix = np.zeros((dom_height, dom_width), dtype=object)
        coverage_count = np.zeros((dom_height, dom_width), dtype=int)

        # 简化的映射计算：基于空间网格划分
        # 假设DOM是由原始影像按网格排列生成的
        grid_rows = 6  # 假设6行
        grid_cols = 6  # 假设6列

        cell_height = dom_height // grid_rows
        cell_width = dom_width // grid_cols

        image_id = 1
        for grid_row in range(grid_rows):
            for grid_col in range(grid_cols):
                if image_id <= len(self.original_images):
                    # 计算当前网格的像元范围
                    start_row = grid_row * cell_height
                    end_row = min((grid_row + 1) * cell_height, dom_height)
                    start_col = grid_col * cell_width
                    end_col = min((grid_col + 1) * cell_width, dom_width)

                    # 为该网格区域的所有像元分配对应的原始影像
                    for r in range(start_row, end_row):
                        for c in range(start_col, end_col):
                            # 模拟重叠：边界区域可能被多张影像覆盖
                            covering_images = [image_id]

                            # 添加相邻影像的重叠
                            if grid_row > 0 and r < start_row + cell_height // 4:  # 上边界重叠
                                prev_id = image_id - grid_cols
                                if prev_id > 0:
                                    covering_images.append(prev_id)

                            if grid_col > 0 and c < start_col + cell_width // 4:  # 左边界重叠
                                prev_id = image_id - 1
                                if prev_id > 0 and (prev_id - 1) // grid_cols == grid_row:
                                    covering_images.append(prev_id)

                            mapping_matrix[r, c] = covering_images.copy()
                            coverage_count[r, c] = len(covering_images)

                    image_id += 1

        # 保存映射结果
        self.pixel_mapping = {
            'mapping_matrix': mapping_matrix,
            'coverage_count': coverage_count,
            'dom_width': dom_width,
            'dom_height': dom_height
        }

        # 统计覆盖情况
        self.calculate_coverage_statistics(coverage_count)

        print("   ✅ 像元映射关系计算完成")
    
    def point_in_bounds(self, x, y, bounds):
        """判断点是否在边界内"""
        return (bounds['min_lon'] <= x <= bounds['max_lon'] and 
                bounds['min_lat'] <= y <= bounds['max_lat'])
    
    def calculate_coverage_statistics(self, coverage_count):
        """计算覆盖统计信息"""
        print("📊 正在计算覆盖统计...")
        
        unique_counts, pixel_counts = np.unique(coverage_count, return_counts=True)
        
        total_pixels = coverage_count.size
        covered_pixels = np.sum(coverage_count > 0)
        
        self.coverage_stats = {
            'total_pixels': int(total_pixels),
            'covered_pixels': int(covered_pixels),
            'coverage_rate': float(covered_pixels / total_pixels * 100),
            'coverage_distribution': {}
        }
        
        for count, pixels in zip(unique_counts, pixel_counts):
            self.coverage_stats['coverage_distribution'][int(count)] = int(pixels)
        
        print(f"   📈 总像元数: {total_pixels:,}")
        print(f"   📈 覆盖像元数: {covered_pixels:,}")
        print(f"   📈 覆盖率: {self.coverage_stats['coverage_rate']:.2f}%")
        
        for count, pixels in zip(unique_counts, pixel_counts):
            if count > 0:
                percentage = pixels / total_pixels * 100
                print(f"   📈 {count}张影像覆盖: {pixels:,} 像元 ({percentage:.2f}%)")
    
    def create_coverage_visualization(self):
        """创建覆盖关系可视化"""
        print("🎨 正在创建覆盖关系可视化...")

        if not self.pixel_mapping:
            print("❌ 像元映射数据不存在")
            return

        coverage_count = self.pixel_mapping['coverage_count']

        # 数据预处理和调试信息
        print(f"   📊 覆盖数据形状: {coverage_count.shape}")
        print(f"   📊 数据类型: {coverage_count.dtype}")
        print(f"   📊 数据范围: {np.min(coverage_count)} - {np.max(coverage_count)}")

        # 确保数据是数值类型
        coverage_count = coverage_count.astype(np.int32)

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('DOM-原始影像对应关系可视化分析', fontsize=16, fontweight='bold', fontfamily=CHINESE_FONT)

        # 1. 覆盖数量热图（降采样显示）
        ax1 = axes[0, 0]
        max_coverage = int(np.max(coverage_count))
        min_coverage = int(np.min(coverage_count))

        print(f"   📊 覆盖范围: {min_coverage} - {max_coverage}")

        # 降采样以避免图像过大
        sample_factor = max(1, max(coverage_count.shape) // 500)  # 减小采样因子
        coverage_sampled = coverage_count[::sample_factor, ::sample_factor]

        print(f"   📊 采样后形状: {coverage_sampled.shape}")
        print(f"   📊 采样因子: {sample_factor}")

        # 使用标准colormap
        im1 = ax1.imshow(coverage_sampled, cmap='viridis', vmin=min_coverage, vmax=max_coverage, aspect='auto')
        ax1.set_title(f'DOM像元覆盖数量分布\n(采样显示: {coverage_sampled.shape})', fontfamily=CHINESE_FONT)
        ax1.set_xlabel('DOM列坐标（采样）', fontfamily=CHINESE_FONT)
        ax1.set_ylabel('DOM行坐标（采样）', fontfamily=CHINESE_FONT)
        cbar1 = plt.colorbar(im1, ax=ax1, label='覆盖影像数量')
        cbar1.set_label('覆盖影像数量', fontfamily=CHINESE_FONT)
        cbar1.set_ticks(range(min_coverage, max_coverage + 1))
        
        # 2. 覆盖统计柱状图
        ax2 = axes[0, 1]
        coverage_dist = self.coverage_stats['coverage_distribution']
        counts = list(coverage_dist.keys())
        pixels = list(coverage_dist.values())

        # 创建颜色列表
        colors = []
        for c in counts:
            if c == 0:
                colors.append('lightgray')
            elif c == 1:
                colors.append('lightblue')
            elif c == 2:
                colors.append('orange')
            elif c == 3:
                colors.append('red')
            else:
                colors.append('darkred')

        bars = ax2.bar(counts, pixels, color=colors, alpha=0.7, edgecolor='black')
        ax2.set_title('覆盖数量统计分布', fontfamily=CHINESE_FONT)
        ax2.set_xlabel('覆盖影像数量', fontfamily=CHINESE_FONT)
        ax2.set_ylabel('像元数量', fontfamily=CHINESE_FONT)
        ax2.set_yscale('log')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, pixels):
            height = bar.get_height()
            if height > 0:  # 只为非零值添加标签
                ax2.text(bar.get_x() + bar.get_width()/2., height * 1.1,
                        f'{count:,}', ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        # 3. 原始影像分布示意图
        ax3 = axes[1, 0]

        # 创建6x6网格显示影像分布
        grid_size = 6
        cell_width = 1.0
        cell_height = 1.0

        image_id = 1
        for row in range(grid_size):
            for col in range(grid_size):
                if image_id <= len(self.original_images):
                    # 计算矩形位置
                    x = col * cell_width
                    y = (grid_size - 1 - row) * cell_height  # 翻转Y轴

                    # 根据覆盖情况选择颜色
                    if image_id <= 24:  # 前24张影像
                        color = f'C{(image_id-1) % 10}'
                        alpha = 0.6
                    else:  # 后面的影像
                        color = 'gray'
                        alpha = 0.4

                    rect = patches.Rectangle(
                        (x, y), cell_width * 0.9, cell_height * 0.9,
                        linewidth=2, edgecolor='black', facecolor=color, alpha=alpha
                    )
                    ax3.add_patch(rect)

                    # 添加影像编号
                    center_x = x + cell_width * 0.45
                    center_y = y + cell_height * 0.45
                    ax3.text(center_x, center_y, str(image_id),
                            ha='center', va='center', fontsize=10, fontweight='bold', color='white')

                    image_id += 1

        ax3.set_xlim(-0.1, grid_size * cell_width)
        ax3.set_ylim(-0.1, grid_size * cell_height)
        ax3.set_title('原始影像网格分布示意图\n(6×6网格，34张影像)', fontfamily=CHINESE_FONT)
        ax3.set_xlabel('网格列', fontfamily=CHINESE_FONT)
        ax3.set_ylabel('网格行', fontfamily=CHINESE_FONT)
        ax3.grid(True, alpha=0.3)
        ax3.set_aspect('equal')
        
        # 4. 覆盖率饼图
        ax4 = axes[1, 1]
        coverage_labels = []
        coverage_sizes = []
        coverage_colors = []

        # 定义更好的颜色方案
        color_map = {
            0: ('未覆盖', 'lightgray'),
            1: ('单影像覆盖', 'lightblue'),
            2: ('双影像覆盖', 'orange'),
            3: ('三影像覆盖', 'red')
        }

        for count, pixels in coverage_dist.items():
            if count in color_map:
                label, color = color_map[count]
            else:
                label = f'{count}张影像覆盖'
                color = 'darkred'

            coverage_labels.append(label)
            coverage_colors.append(color)
            coverage_sizes.append(pixels)

        # 计算百分比
        total_pixels = sum(coverage_sizes)
        percentages = [size/total_pixels*100 for size in coverage_sizes]

        wedges, texts, autotexts = ax4.pie(coverage_sizes, labels=coverage_labels,
                                          colors=coverage_colors, autopct='%1.1f%%',
                                          startangle=90, textprops={'fontsize': 10, 'fontfamily': CHINESE_FONT})
        ax4.set_title('DOM像元覆盖率分布\n(总计: {:,} 像元)'.format(total_pixels), fontfamily=CHINESE_FONT)

        # 添加图例
        ax4.legend(wedges, [f'{label}: {size:,} 像元' for label, size in zip(coverage_labels, coverage_sizes)],
                  title="覆盖统计", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
                  fontsize=8, prop={'family': CHINESE_FONT})

        plt.tight_layout()

        # 保存图像，使用更高质量
        plt.savefig('dom_original_mapping_visualization_fixed.png', dpi=150, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print("   ✅ 修正版可视化图表已保存: dom_original_mapping_visualization_fixed.png")
    
    def create_detailed_mapping_report(self):
        """创建详细的映射关系报告"""
        print("📋 正在生成详细映射关系报告...")
        
        # 准备报告数据
        report_data = {
            'dom_info': {
                'file_path': self.dom_path,
                'dimensions': {
                    'width': self.pixel_mapping['dom_width'],
                    'height': self.pixel_mapping['dom_height']
                },
                'coordinate_system': 'Simplified Grid System',
                'processing_method': 'Grid-based mapping simulation'
            },
            'original_images': [
                {
                    'id': img['id'],
                    'filename': img['filename'],
                    'dimensions': {'width': img['width'], 'height': img['height']},
                    'estimated_bounds': img['bounds']
                }
                for img in self.original_images
            ],
            'coverage_statistics': self.coverage_stats,
            'mapping_summary': {
                'total_dom_pixels': self.coverage_stats['total_pixels'],
                'covered_pixels': self.coverage_stats['covered_pixels'],
                'coverage_percentage': self.coverage_stats['coverage_rate'],
                'max_overlap': max(self.coverage_stats['coverage_distribution'].keys()),
                'average_overlap': self.calculate_average_overlap()
            }
        }
        
        # 保存JSON报告
        with open('dom_original_mapping_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 详细报告已保存: dom_original_mapping_report.json")
    
    def calculate_average_overlap(self):
        """计算平均重叠度"""
        coverage_dist = self.coverage_stats['coverage_distribution']
        total_weighted = sum(count * pixels for count, pixels in coverage_dist.items())
        total_pixels = sum(coverage_dist.values())
        return total_weighted / total_pixels if total_pixels > 0 else 0
    
    def run_complete_analysis(self):
        """运行完整的DOM-原始影像对应关系分析"""
        print("=" * 70)
        print("🗺️ DOM-原始影像对应关系分析系统")
        print("=" * 70)
        
        # 1. 加载原始影像信息
        self.load_original_images()
        
        if not self.original_images:
            print("❌ 没有找到原始影像")
            return
        
        # 2. 加载DOM数据
        if not self.load_dom_data():
            return
        
        # 3. 计算像元映射关系
        self.calculate_pixel_mapping()
        
        # 4. 创建可视化
        self.create_coverage_visualization()
        
        # 5. 生成详细报告
        self.create_detailed_mapping_report()
        
        print("\n" + "=" * 70)
        print("🎯 DOM-原始影像对应关系分析完成！")
        print("=" * 70)
        print("📁 生成的文件:")
        print("   • dom_original_mapping_visualization.png - 对应关系可视化")
        print("   • dom_original_mapping_report.json - 详细分析报告")
        
        print(f"\n📊 分析结果摘要:")
        print(f"   • DOM总像元数: {self.coverage_stats['total_pixels']:,}")
        print(f"   • 覆盖像元数: {self.coverage_stats['covered_pixels']:,}")
        print(f"   • 覆盖率: {self.coverage_stats['coverage_rate']:.2f}%")
        print(f"   • 平均重叠度: {self.calculate_average_overlap():.2f}")
        
        print("\n✅ DOM中每个像元与原始影像的对应关系已建立并可视化！")

def main():
    """主函数"""
    system = DOMOriginalMappingSystem()
    system.run_complete_analysis()

if __name__ == "__main__":
    main()
