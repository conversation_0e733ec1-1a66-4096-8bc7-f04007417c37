#!/usr/bin/env python3
"""
结果可视化工具
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
import os
import matplotlib
from matplotlib import font_manager

# 设置中文字体
def setup_chinese_font():
    """设置matplotlib中文字体"""
    # 尝试常见的中文字体
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'FangSong',        # 仿宋
        'DejaVu Sans'      # 备用字体
    ]

    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"使用字体: {font_name}")
            return True
        except:
            continue

    # 如果都不可用，使用英文标签
    print("警告: 无法找到合适的中文字体，将使用英文标签")
    return False

# 初始化字体设置
CHINESE_FONT_AVAILABLE = setup_chinese_font()

class ResultVisualizer:
    """结果可视化器"""
    
    def __init__(self):
        pass
    
    def visualize_original_annotations(self, image_path: str, annotations: list, output_path: str = None):
        """可视化原始图像上的标注"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image.shape[:2]
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 9))
        ax.imshow(image_rgb)
        
        # 绘制标注
        for i, annotation in enumerate(annotations):
            points = annotation['points']
            labels = annotation['labels']
            
            # 转换百分比坐标到像素坐标
            pixel_points = []
            for point in points:
                x_percent, y_percent = point
                x_pixel = (x_percent / 100.0) * width
                y_pixel = (y_percent / 100.0) * height
                pixel_points.append([x_pixel, y_pixel])
            
            # 创建多边形
            polygon = Polygon(pixel_points, fill=False, edgecolor='red', linewidth=2)
            ax.add_patch(polygon)
            
            # 添加标签
            if pixel_points:
                center_x = np.mean([p[0] for p in pixel_points])
                center_y = np.mean([p[1] for p in pixel_points])
                ax.text(center_x, center_y, f"{labels[0]}\n#{i+1}", 
                       color='yellow', fontsize=8, ha='center', va='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
        
        title = f"Original Annotations: {os.path.basename(image_path)}" if not CHINESE_FONT_AVAILABLE else f"原始标注: {os.path.basename(image_path)}"
        ax.set_title(title)
        ax.axis('off')
        
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            print(f"可视化结果已保存: {output_path}")
        else:
            plt.show()
        
        plt.close()
    
    def visualize_geographic_annotations(self, geo_file: str, output_path: str = None):
        """可视化地理坐标标注"""
        with open(geo_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        bounds = data['bounds']
        annotations = data['annotations']
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 9))
        
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        color_idx = 0
        
        # 绘制每个图像的标注
        for image_name, image_annotations in annotations.items():
            color = colors[color_idx % len(colors)]
            
            for annotation in image_annotations:
                coords = annotation['geometry']['coordinates'][0]
                
                # 提取经纬度
                lons = [coord[0] for coord in coords]
                lats = [coord[1] for coord in coords]
                
                # 绘制多边形
                ax.plot(lons + [lons[0]], lats + [lats[0]], 
                       color=color, linewidth=2, label=image_name if annotation == image_annotations[0] else "")
                ax.fill(lons, lats, color=color, alpha=0.3)
            
            color_idx += 1
        
        # 设置边界
        min_lon, min_lat, max_lon, max_lat = bounds
        margin = 0.0001  # 添加边距
        ax.set_xlim(min_lon - margin, max_lon + margin)
        ax.set_ylim(min_lat - margin, max_lat + margin)
        
        xlabel = 'Longitude (degrees)' if not CHINESE_FONT_AVAILABLE else '经度 (度)'
        ylabel = 'Latitude (degrees)' if not CHINESE_FONT_AVAILABLE else '纬度 (度)'
        title = 'Geographic Annotation Distribution' if not CHINESE_FONT_AVAILABLE else '地理坐标标注分布'

        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            print(f"地理标注可视化已保存: {output_path}")
        else:
            plt.show()
        
        plt.close()
    
    def visualize_raster_result(self, raster_file: str, geo_info_file: str = None, output_path: str = None):
        """可视化栅格结果"""
        # 尝试读取不同格式的栅格文件
        if raster_file.endswith('.png'):
            raster = cv2.imread(raster_file, cv2.IMREAD_GRAYSCALE)
            if raster is not None:
                raster = (raster > 0).astype(np.uint8)  # 转换为二值
        else:
            try:
                import rasterio
                with rasterio.open(raster_file) as src:
                    raster = src.read(1)
            except ImportError:
                print("rasterio未安装，无法读取GeoTIFF文件")
                return
            except Exception as e:
                print(f"无法读取栅格文件: {e}")
                return
        
        if raster is None:
            print(f"无法读取栅格文件: {raster_file}")
            return
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 显示栅格
        im1 = ax1.imshow(raster, cmap='viridis', interpolation='nearest')

        title1 = 'Raster Labels' if not CHINESE_FONT_AVAILABLE else '栅格标签'
        xlabel1 = 'X (pixels)' if not CHINESE_FONT_AVAILABLE else 'X (像素)'
        ylabel1 = 'Y (pixels)' if not CHINESE_FONT_AVAILABLE else 'Y (像素)'
        colorbar_label = 'Label Value' if not CHINESE_FONT_AVAILABLE else '标签值'

        ax1.set_title(title1)
        ax1.set_xlabel(xlabel1)
        ax1.set_ylabel(ylabel1)
        plt.colorbar(im1, ax=ax1, label=colorbar_label)

        # 显示统计直方图
        unique, counts = np.unique(raster, return_counts=True)
        ax2.bar(unique, counts, alpha=0.7)

        xlabel2 = 'Label Value' if not CHINESE_FONT_AVAILABLE else '标签值'
        ylabel2 = 'Pixel Count' if not CHINESE_FONT_AVAILABLE else '像素数量'
        title2 = 'Label Distribution Statistics' if not CHINESE_FONT_AVAILABLE else '标签分布统计'

        ax2.set_xlabel(xlabel2)
        ax2.set_ylabel(ylabel2)
        ax2.set_title(title2)
        
        # 添加统计文本
        total_pixels = raster.size
        for value, count in zip(unique, counts):
            percentage = (count / total_pixels) * 100
            if CHINESE_FONT_AVAILABLE:
                label_name = "背景" if value == 0 else "False Positive-Confusing"
            else:
                label_name = "Background" if value == 0 else "False Positive-Confusing"
            ax2.text(value, count, f'{label_name}\n{count:,}\n({percentage:.1f}%)',
                    ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            print(f"栅格可视化已保存: {output_path}")
        else:
            plt.show()
        
        plt.close()
    
    def create_summary_report(self, image_dir: str, label_file: str, 
                            geo_file: str = 'geographic_annotations.json',
                            stats_file: str = 'labels_stats.json'):
        """创建处理结果摘要报告"""
        print("=== 处理结果摘要报告 ===\n")
        
        # 1. 输入数据统计
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg'))]
        print(f"1. 输入数据:")
        print(f"   - 图像文件数量: {len(image_files)}")
        print(f"   - 标注文件: {label_file}")
        
        # 2. 标注数据统计
        if os.path.exists(label_file):
            with open(label_file, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            
            total_annotations = 0
            annotated_images = 0
            
            for task in label_data:
                if task['annotations']:
                    annotated_images += 1
                    for annotation in task['annotations']:
                        total_annotations += len(annotation['result'])
            
            print(f"   - 已标注图像数量: {annotated_images}")
            print(f"   - 总标注数量: {total_annotations}")
        
        # 3. 地理处理结果
        if os.path.exists(geo_file):
            with open(geo_file, 'r', encoding='utf-8') as f:
                geo_data = json.load(f)
            
            bounds = geo_data['bounds']
            print(f"\n2. 地理处理结果:")
            print(f"   - 地理边界: {bounds}")
            print(f"   - 经度范围: {bounds[2] - bounds[0]:.6f} 度")
            print(f"   - 纬度范围: {bounds[3] - bounds[1]:.6f} 度")
        
        # 4. 栅格化结果
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            print(f"\n3. 栅格化结果:")
            print(f"   - 总像素数: {stats['total_pixels']:,}")
            
            for label, info in stats['label_coverage'].items():
                print(f"   - {label}: {info['pixels']:,} 像素 ({info['percentage']}%)")
        
        print(f"\n4. 输出文件:")
        output_files = [
            'geographic_annotations.json',
            'labels.tif',
            'labels.tfw',
            'labels.png',
            'labels_geo_info.json',
            'labels_stats.json'
        ]
        
        for file in output_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"   - {file} ({size:,} bytes)")

def main():
    """主函数 - 创建可视化"""
    visualizer = ResultVisualizer()
    
    # 创建摘要报告
    visualizer.create_summary_report('.', 'label.json')
    
    # 可视化地理标注（如果存在）
    if os.path.exists('geographic_annotations.json'):
        visualizer.visualize_geographic_annotations(
            'geographic_annotations.json', 
            'geographic_annotations_plot.png'
        )
    
    # 可视化栅格结果（如果存在）
    if os.path.exists('labels.png'):
        visualizer.visualize_raster_result(
            'labels.png', 
            'labels_geo_info.json',
            'raster_result_plot.png'
        )
    elif os.path.exists('labels.tif'):
        visualizer.visualize_raster_result(
            'labels.tif',
            output_path='raster_result_plot.png'
        )

if __name__ == "__main__":
    main()
