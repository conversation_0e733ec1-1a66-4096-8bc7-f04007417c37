#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的DOM语义分割系统 - 正确处理UTM坐标系和透明度
基于真实地理坐标建立DOM-原始影像对应关系，实现精确的标签映射
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 地理处理相关导入
try:
    import rasterio
    from rasterio.warp import transform
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio和pyproj可用，将使用高精度地理坐标转换")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio或pyproj不可用，无法进行地理坐标转换")

class FixedDOMSegmentation:
    def __init__(self):
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.dom_bounds = None
        self.geographic_annotations = {}
        self.dom_segmentation = None
        self.transformer = None  # UTM到WGS84的转换器
        
        # 标签颜色映射 - 根据实际标注数据调整
        self.label_colors = {
            'False Positive-Confusing (1)': 1,
            'Deciduous Vegetation (2)': 2,
            'Building': 3,
            'Road': 4,
            'Water': 5,
            'Vegetation': 6,
            'Background': 0
        }
        
        self.color_map = {
            0: (0, 0, 0),       # 背景 - 黑色
            1: (255, 0, 0),     # False Positive-Confusing - 红色
            2: (0, 255, 0),     # Deciduous Vegetation - 绿色
            3: (0, 0, 255),     # Building - 蓝色
            4: (255, 255, 0),   # Road - 黄色
            5: (0, 255, 255),   # Water - 青色
            6: (255, 0, 255),   # Vegetation - 洋红
        }
    
    def load_dom_data(self):
        """加载DOM数据和地理参考信息"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        if not RASTERIO_AVAILABLE:
            print("❌ 需要rasterio库进行地理坐标转换")
            return False
        
        try:
            # 使用rasterio读取DOM数据和地理信息
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()  # 读取所有波段
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                self.dom_bounds = src.bounds
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM波段数: {src.count}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                print(f"   ✅ DOM边界(UTM): {self.dom_bounds}")
                
                # 创建UTM到WGS84的坐标转换器
                self.transformer = Transformer.from_crs(self.dom_crs, 'EPSG:4326', always_xy=True)
                
                # 转换DOM边界到WGS84以便比较
                min_lon, min_lat = self.transformer.transform(self.dom_bounds.left, self.dom_bounds.bottom)
                max_lon, max_lat = self.transformer.transform(self.dom_bounds.right, self.dom_bounds.top)
                print(f"   ✅ DOM边界(WGS84): 经度 {min_lon:.6f} - {max_lon:.6f}, 纬度 {min_lat:.6f} - {max_lat:.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_geographic_annotations(self):
        """加载地理坐标标注数据"""
        print("🌍 正在加载地理坐标标注数据...")
        
        geo_file = 'geographic_annotations.json'
        if not os.path.exists(geo_file):
            print("❌ 地理标注文件不存在")
            return False
        
        try:
            with open(geo_file, 'r', encoding='utf-8') as f:
                geo_data = json.load(f)
            
            self.geographic_annotations = geo_data.get('annotations', {})
            print(f"   ✅ 加载了 {len(self.geographic_annotations)} 张图像的地理标注数据")
            
            # 显示地理边界信息
            if 'bounds' in geo_data:
                bounds = geo_data['bounds']
                print(f"   ✅ 标注边界: 经度 {bounds[0]:.6f} - {bounds[2]:.6f}, 纬度 {bounds[1]:.6f} - {bounds[3]:.6f}")
            
            # 分析第一个标注的结构
            if self.geographic_annotations:
                first_image = list(self.geographic_annotations.keys())[0]
                first_annotations = self.geographic_annotations[first_image]
                print(f"   📋 第一张图像 {first_image} 有 {len(first_annotations)} 个标注")
                
                if first_annotations:
                    first_annotation = first_annotations[0]
                    print(f"   📋 标注结构: {list(first_annotation.keys())}")
                    if 'properties' in first_annotation:
                        print(f"   📋 属性字段: {list(first_annotation['properties'].keys())}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载地理标注数据失败: {e}")
            return False
    
    def point_in_polygon(self, x, y, polygon_coords):
        """使用射线法判断点是否在多边形内"""
        n = len(polygon_coords)
        inside = False
        
        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def get_label_at_geographic_point(self, lon, lat, image_name):
        """获取指定地理点在指定影像中的标签"""
        if image_name not in self.geographic_annotations:
            return 0
        
        annotations = self.geographic_annotations[image_name]
        
        # 检查点是否在任何标注多边形内
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                
                # 使用射线法判断点是否在多边形内
                if self.point_in_polygon(lon, lat, coords):
                    # 获取标签ID
                    if 'properties' in annotation:
                        # 尝试不同的标签字段名
                        for label_field in ['label', 'class', 'category', 'type']:
                            if label_field in annotation['properties']:
                                label_name = annotation['properties'][label_field]
                                return self.label_colors.get(label_name, 2)  # 默认返回植被标签
                    
                    # 如果没有找到标签信息，根据几何特征推断
                    # 这里可以根据多边形的大小、形状等特征来推断标签
                    return 2  # 默认返回植被标签
        
        return 0  # 背景
    
    def point_in_image_coverage(self, lon, lat, annotations):
        """判断地理点是否在影像的覆盖范围内"""
        if not annotations:
            return False
        
        # 计算所有标注的边界框
        all_lons = []
        all_lats = []
        
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                for coord in coords:
                    all_lons.append(coord[0])
                    all_lats.append(coord[1])
        
        if not all_lons or not all_lats:
            return False
        
        # 扩展边界框以包含影像的完整覆盖范围
        min_lon, max_lon = min(all_lons), max(all_lons)
        min_lat, max_lat = min(all_lats), max(all_lats)
        
        # 扩展边界（假设影像覆盖范围比标注范围大）
        lon_range = max_lon - min_lon
        lat_range = max_lat - min_lat
        
        # 扩展200%的范围以确保覆盖整个影像
        expansion_factor = 2.0
        min_lon -= lon_range * expansion_factor
        max_lon += lon_range * expansion_factor
        min_lat -= lat_range * expansion_factor
        max_lat += lat_range * expansion_factor
        
        # 判断点是否在扩展的边界框内
        return min_lon <= lon <= max_lon and min_lat <= lat <= max_lat

    def map_labels_to_dom(self):
        """基于地理坐标将原始影像标签映射到DOM"""
        print("🎯 正在基于地理坐标将原始影像标签映射到DOM...")

        if not self.geographic_annotations:
            print("❌ 地理标注数据未加载")
            return False

        # DOM数据形状: (bands, height, width)
        bands, dom_height, dom_width = self.dom_data.shape
        print(f"   📐 DOM尺寸: {dom_width} x {dom_height}, {bands} 波段")

        # 检查是否有Alpha通道（透明度）
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]  # 第4波段是Alpha通道
            valid_mask = alpha_channel > 0  # 非透明区域
            print(f"   🎭 检测到Alpha通道，有效像元: {np.sum(valid_mask):,} / {dom_height * dom_width:,}")
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
            print(f"   📊 无Alpha通道，处理所有像元: {dom_height * dom_width:,}")

        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)

        print(f"   📸 可用影像: {len(self.geographic_annotations)} 张")

        # 使用更大的采样步长以提高速度，但在有效区域内密集采样
        sample_step = 20
        processed_pixels = 0
        total_pixels = 0

        # 计算需要处理的像元数
        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                if not has_alpha or valid_mask[row, col]:
                    total_pixels += 1

        print(f"   🗺️ 开始基于地理坐标的标签映射，预计处理 {total_pixels:,} 个采样点...")

        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                # 跳过透明区域
                if has_alpha and not valid_mask[row, col]:
                    continue

                # 将DOM像元坐标转换为UTM坐标
                utm_x, utm_y = self.dom_transform * (col, row)

                # 转换为WGS84地理坐标
                try:
                    geo_lon, geo_lat = self.transformer.transform(utm_x, utm_y)
                except Exception as e:
                    print(f"   ⚠️ 坐标转换失败: {e}")
                    continue

                # 收集所有覆盖影像的标签投票
                labels_votes = {}
                covering_images = []

                # 找到覆盖此地理位置的所有原始影像
                for image_name, annotations in self.geographic_annotations.items():
                    if self.point_in_image_coverage(geo_lon, geo_lat, annotations):
                        covering_images.append(image_name)

                        # 获取该影像在此地理位置的标签
                        label = self.get_label_at_geographic_point(geo_lon, geo_lat, image_name)
                        if label > 0:  # 非背景标签
                            labels_votes[label] = labels_votes.get(label, 0) + 1

                # 选择得票最多的标签
                if labels_votes:
                    final_label = max(labels_votes.keys(), key=lambda k: labels_votes[k])

                    # 填充邻域区域（只在有效区域内）
                    for r in range(max(0, row-sample_step//2), min(dom_height, row+sample_step+sample_step//2)):
                        for c in range(max(0, col-sample_step//2), min(dom_width, col+sample_step+sample_step//2)):
                            if (not has_alpha or valid_mask[r, c]) and self.dom_segmentation[r, c] == 0:
                                self.dom_segmentation[r, c] = final_label

                processed_pixels += 1
                if processed_pixels % 100 == 0:
                    progress = (processed_pixels / total_pixels) * 100
                    print(f"   📊 映射进度: {progress:.1f}% ({processed_pixels}/{total_pixels})")

        print(f"   ✅ 标签映射完成")

        # 统计分割结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_dom_pixels = dom_height * dom_width

        print(f"   📊 分割结果统计:")
        label_name_map = {v: k for k, v in self.label_colors.items()}
        for label, count in zip(unique_labels, counts):
            label_name = label_name_map.get(label, f"Unknown({label})")
            percentage = (count / total_dom_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")

        return True

    def save_results(self):
        """保存分割结果"""
        print("💾 正在保存分割结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码（灰度图）
        cv2.imwrite('fixed_dom_segmentation_mask.png', self.dom_segmentation)

        # 创建彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        # 保存彩色分割结果
        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_dom_segmentation_result.png', segmentation_bgr)

        # 创建叠加可视化（使用DOM的RGB通道）
        if self.dom_data.shape[0] >= 3:
            # 转换DOM数据为RGB格式 (height, width, 3)
            dom_rgb = np.transpose(self.dom_data[:3], (1, 2, 0))
            dom_rgb = np.clip(dom_rgb, 0, 255).astype(np.uint8)
        else:
            # 如果只有一个波段，转换为RGB
            dom_gray = self.dom_data[0]
            dom_rgb = np.stack([dom_gray, dom_gray, dom_gray], axis=2)
            dom_rgb = np.clip(dom_rgb, 0, 255).astype(np.uint8)

        # 创建叠加图像
        overlay = dom_rgb.copy().astype(np.float32)
        seg_mask = np.any(segmentation_rgb > 0, axis=2)  # 非背景区域

        # 在有标注的区域进行叠加
        overlay[seg_mask] = overlay[seg_mask] * 0.6 + segmentation_rgb[seg_mask].astype(np.float32) * 0.4
        overlay = np.clip(overlay, 0, 255).astype(np.uint8)

        # 保存叠加结果
        overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('fixed_dom_segmentation_overlay.png', overlay_bgr)

        print("   ✅ 分割掩码已保存: fixed_dom_segmentation_mask.png")
        print("   ✅ 分割结果已保存: fixed_dom_segmentation_result.png")
        print("   ✅ 叠加可视化已保存: fixed_dom_segmentation_overlay.png")

    def run_fixed_segmentation(self):
        """运行修正的DOM分割系统"""
        print("=" * 80)
        print("🎯 修正的DOM语义分割系统 (正确处理UTM坐标和透明度)")
        print("=" * 80)

        # 1. 加载DOM数据
        if not self.load_dom_data():
            return False

        # 2. 加载地理坐标标注数据
        if not self.load_geographic_annotations():
            return False

        # 3. 执行标签映射和DOM分割
        if not self.map_labels_to_dom():
            return False

        # 4. 保存结果
        self.save_results()

        print("\n" + "=" * 80)
        print("🎯 修正的DOM语义分割完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • fixed_dom_segmentation_result.png - 彩色分割结果")
        print("   • fixed_dom_segmentation_mask.png - 灰度分割掩码")
        print("   • fixed_dom_segmentation_overlay.png - DOM与分割结果叠加")

        # 输出统计摘要
        if self.dom_segmentation is not None:
            unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
            total_pixels = self.dom_segmentation.shape[0] * self.dom_segmentation.shape[1]

            print(f"\n📊 分割结果摘要:")
            print(f"   • DOM尺寸: {self.dom_segmentation.shape[1]} × {self.dom_segmentation.shape[0]}")
            print(f"   • 总像元数: {total_pixels:,}")

            label_name_map = {v: k for k, v in self.label_colors.items()}
            for label, count in zip(unique_labels, counts):
                if count > 1000:  # 只显示主要类别
                    label_name = label_name_map.get(label, f"Unknown({label})")
                    percentage = (count / total_pixels) * 100
                    print(f"   • {label_name}: {count:,} 像元 ({percentage:.2f}%)")

        print("\n✅ 修正的DOM语义分割系统运行完成！")
        print("🎨 现在您可以查看生成的分割结果和可视化文件")
        print("\n🔧 主要修正:")
        print("   ✅ 正确处理UTM坐标系到WGS84的转换")
        print("   ✅ 正确处理DOM的Alpha通道（透明度）")
        print("   ✅ 使用rasterio读取完整的地理参考信息")
        print("   ✅ 改进的标签映射逻辑和投票机制")

        return True

def main():
    """主函数"""
    system = FixedDOMSegmentation()
    success = system.run_fixed_segmentation()

    if success:
        print("\n🎉 DOM分割任务成功完成！")
        print("📋 关键改进:")
        print("   ✅ 使用正确的UTM到WGS84坐标转换")
        print("   ✅ 处理DOM的透明区域")
        print("   ✅ 基于真实地理位置进行精确的标签映射")
        print("   ✅ 支持多影像重叠区域的标签投票")
        print("   ✅ 生成高质量的分割结果和可视化")
    else:
        print("\n❌ DOM分割任务失败，请检查错误信息")

if __name__ == "__main__":
    main()
