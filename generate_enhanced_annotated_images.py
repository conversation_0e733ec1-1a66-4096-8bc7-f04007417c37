#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DJI影像增强标注可视化系统
为每张原始图像生成带边界框和详细标签的可视化结果
"""

import json
import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import urllib.parse
from datetime import datetime

class EnhancedAnnotatedImageGenerator:
    def __init__(self):
        self.annotations = {}
        self.label_colors = {
            'False Positive-Confusing (1)': (255, 0, 0),      # 红色
            'Deciduous Vegetation (2)': (0, 255, 0),          # 绿色
            'Building': (0, 0, 255),                           # 蓝色
            'Road': (255, 255, 0),                             # 黄色
            'Water': (0, 255, 255),                            # 青色
            'Vegetation': (255, 0, 255)                        # 紫色
        }
        
    def load_annotations(self):
        """加载标注数据"""
        print("🏷️ 正在加载标注数据...")
        
        try:
            with open('label.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for item in data:
                # 提取图像文件名
                image_path = item['data']['image']
                raw_filename = os.path.basename(image_path)
                
                # 从URL编码的路径中提取真实文件名
                decoded_path = urllib.parse.unquote(raw_filename)
                
                # 进一步提取文件名
                if '\\' in decoded_path:
                    filename = decoded_path.split('\\')[-1]
                elif '/' in decoded_path:
                    filename = decoded_path.split('/')[-1]
                else:
                    filename = decoded_path
                
                print(f"🔍 处理文件: {raw_filename} -> {filename}")
                
                # 提取标注
                annotations = []
                if 'annotations' in item and item['annotations']:
                    for annotation in item['annotations']:
                        if 'result' in annotation:
                            for result in annotation['result']:
                                if result['type'] == 'polygonlabels':
                                    points = result['value']['points']
                                    labels = result['value']['polygonlabels']
                                    
                                    # 存储相对坐标（百分比）
                                    annotations.append({
                                        'type': 'polygon',
                                        'points': points,  # 保持百分比坐标
                                        'label': labels[0] if labels else 'Unknown'
                                    })
                                    
                                    print(f"   ✅ 添加标注: {labels[0] if labels else 'Unknown'}, {len(points)} 个点")
                
                if annotations:
                    self.annotations[filename] = annotations
                    print(f"📝 {filename}: {len(annotations)} 个标注")
                else:
                    print(f"⚠️ {filename}: 没有找到标注")
            
            print(f"📋 成功加载 {len(self.annotations)} 张图像的标注数据")
            
        except Exception as e:
            print(f"❌ 加载标注数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_polygon_bbox(self, points):
        """获取多边形的边界框"""
        if not points:
            return None
        
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        return {
            'x_min': min(x_coords),
            'y_min': min(y_coords),
            'x_max': max(x_coords),
            'y_max': max(y_coords)
        }
    
    def draw_enhanced_annotations(self, image_path, annotations):
        """在图像上绘制增强的标注（多边形+边界框）"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None
        
        height, width = image.shape[:2]
        
        # 创建绘制副本
        annotated_image = image.copy()
        
        for i, annotation in enumerate(annotations):
            if annotation['type'] == 'polygon':
                # 转换百分比坐标为绝对坐标
                points = []
                for point in annotation['points']:
                    x = int((point[0] / 100.0) * width)
                    y = int((point[1] / 100.0) * height)
                    points.append([x, y])
                
                # 获取标签颜色
                label = annotation['label']
                color = self.label_colors.get(label, (128, 128, 128))
                
                # 绘制多边形
                if len(points) >= 3:
                    poly_array = np.array(points, dtype=np.int32)
                    
                    # 1. 绘制填充多边形（半透明）
                    overlay = annotated_image.copy()
                    cv2.fillPoly(overlay, [poly_array], color)
                    cv2.addWeighted(annotated_image, 0.8, overlay, 0.2, 0, annotated_image)
                    
                    # 2. 绘制多边形边框（粗线）
                    cv2.polylines(annotated_image, [poly_array], True, color, 3)
                    
                    # 3. 计算并绘制边界框
                    bbox_points = [(point[0] / 100.0 * width, point[1] / 100.0 * height) 
                                 for point in annotation['points']]
                    bbox = self.get_polygon_bbox(bbox_points)
                    
                    if bbox:
                        # 绘制边界框
                        cv2.rectangle(annotated_image,
                                    (int(bbox['x_min']), int(bbox['y_min'])),
                                    (int(bbox['x_max']), int(bbox['y_max'])),
                                    color, 2)
                        
                        # 4. 在边界框左上角添加标签
                        label_text = f"{i+1}. {label}"
                        font = cv2.FONT_HERSHEY_SIMPLEX
                        font_scale = 0.7
                        thickness = 2
                        
                        # 获取文本尺寸
                        (text_width, text_height), baseline = cv2.getTextSize(
                            label_text, font, font_scale, thickness)
                        
                        # 计算标签位置（边界框左上角）
                        label_x = int(bbox['x_min'])
                        label_y = int(bbox['y_min']) - 10
                        
                        # 确保标签不超出图像边界
                        if label_y < text_height + 10:
                            label_y = int(bbox['y_max']) + text_height + 10
                        
                        # 绘制标签背景
                        cv2.rectangle(annotated_image,
                                    (label_x - 5, label_y - text_height - 5),
                                    (label_x + text_width + 5, label_y + baseline + 5),
                                    color, -1)
                        
                        # 绘制标签文本
                        cv2.putText(annotated_image, label_text,
                                  (label_x, label_y),
                                  font, font_scale, (255, 255, 255), thickness)
                        
                        # 5. 在多边形中心添加序号
                        M = cv2.moments(poly_array)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            # 绘制序号圆圈
                            cv2.circle(annotated_image, (cx, cy), 20, color, -1)
                            cv2.circle(annotated_image, (cx, cy), 20, (255, 255, 255), 2)
                            
                            # 绘制序号文本
                            number_text = str(i + 1)
                            (num_width, num_height), _ = cv2.getTextSize(
                                number_text, font, 0.8, 2)
                            cv2.putText(annotated_image, number_text,
                                      (cx - num_width//2, cy + num_height//2),
                                      font, 0.8, (255, 255, 255), 2)
        
        return annotated_image
    
    def create_image_info_overlay(self, image_path, annotations):
        """创建图像信息叠加层"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            return None
        
        height, width = image.shape[:2]
        
        # 创建信息文本
        filename = os.path.basename(image_path)
        info_lines = [
            f"文件: {filename}",
            f"尺寸: {width} × {height}",
            f"标注数: {len(annotations)}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        # 统计标签类型
        label_counts = {}
        for annotation in annotations:
            label = annotation['label']
            label_counts[label] = label_counts.get(label, 0) + 1
        
        for label, count in label_counts.items():
            info_lines.append(f"{label}: {count} 个")
        
        # 在图像上绘制信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        line_height = 25
        
        # 计算信息框尺寸
        max_width = 0
        for line in info_lines:
            (text_width, text_height), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_width = max(max_width, text_width)
        
        info_height = len(info_lines) * line_height + 20
        
        # 绘制信息框背景
        cv2.rectangle(image, (10, 10), (max_width + 30, info_height), (0, 0, 0), -1)
        cv2.rectangle(image, (10, 10), (max_width + 30, info_height), (255, 255, 255), 2)
        
        # 绘制信息文本
        for i, line in enumerate(info_lines):
            y_pos = 35 + i * line_height
            cv2.putText(image, line, (20, y_pos), font, font_scale, (255, 255, 255), thickness)
        
        return image
    
    def generate_all_enhanced_images(self):
        """为所有图像生成增强标注可视化"""
        print("🎨 正在为所有图像生成增强标注可视化...")
        
        # 创建输出目录
        output_dir = "enhanced_annotated_images"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        generated_count = 0
        
        # 遍历所有DJI图像
        for filename in os.listdir('.'):
            if filename.startswith('DJI_') and filename.endswith('.JPG'):
                print(f"🖼️ 处理图像: {filename}")
                
                # 检查是否有标注
                if filename in self.annotations:
                    annotations = self.annotations[filename]
                    
                    # 生成增强标注图像
                    annotated_image = self.draw_enhanced_annotations(filename, annotations)
                    
                    if annotated_image is not None:
                        # 添加信息叠加层
                        final_image = self.create_image_info_overlay(filename, annotations)
                        if final_image is not None:
                            # 将标注叠加到信息图像上
                            annotated_image = self.draw_enhanced_annotations(filename, annotations)
                        
                        # 保存增强标注图像
                        output_path = os.path.join(output_dir, f"enhanced_{filename}")
                        success = cv2.imwrite(output_path, annotated_image)
                        
                        if success:
                            print(f"   ✅ 保存成功: {output_path}")
                            generated_count += 1
                        else:
                            print(f"   ❌ 保存失败: {output_path}")
                    else:
                        print(f"   ❌ 图像处理失败")
                else:
                    print(f"   ⚠️ 没有标注数据，跳过")
        
        print(f"🎯 成功生成 {generated_count} 张增强标注图像")
        return generated_count
    
    def run_enhanced_system(self):
        """运行增强标注可视化系统"""
        print("=" * 70)
        print("🎨 DJI影像增强标注可视化系统")
        print("=" * 70)
        
        # 1. 加载标注数据
        self.load_annotations()
        
        if not self.annotations:
            print("❌ 没有找到标注数据")
            return
        
        # 2. 生成所有增强标注图像
        generated_count = self.generate_all_enhanced_images()
        
        # 3. 输出结果摘要
        total_images = len([f for f in os.listdir('.') if f.startswith('DJI_') and f.endswith('.JPG')])
        annotated_images = len(self.annotations)
        total_annotations = sum(len(annotations) for annotations in self.annotations.values())
        
        print("\n" + "=" * 70)
        print("🎯 增强标注可视化生成完成！")
        print("=" * 70)
        print(f"📸 总图像数: {total_images} 张")
        print(f"🏷️ 有标注图像: {annotated_images} 张")
        print(f"📝 总标注数: {total_annotations} 个")
        print(f"📊 标注覆盖率: {(annotated_images / total_images) * 100:.1f}%")
        print(f"🎨 生成增强图像: {generated_count} 张")
        
        print(f"\n🏷️ 标注类别统计:")
        label_counts = {}
        for annotations in self.annotations.values():
            for annotation in annotations:
                label = annotation['label']
                label_counts[label] = label_counts.get(label, 0) + 1
        
        for label, count in label_counts.items():
            color = self.label_colors.get(label, (128, 128, 128))
            print(f"   • {label}: {count} 个 (颜色: RGB{color})")
        
        print(f"\n📁 输出文件:")
        print(f"   • enhanced_annotated_images/ - 增强标注图像目录")
        
        print("\n✅ 所有原始图像的增强标注可视化已完成！")
        print("🖼️ 每张图像都显示了:")
        print("   • 多边形标注区域（半透明填充）")
        print("   • 边界框（红色/绿色矩形）")
        print("   • 标注序号（圆圈中的数字）")
        print("   • 标签名称（边界框上方）")
        print("   • 图像信息（左上角信息框）")

def main():
    """主函数"""
    generator = EnhancedAnnotatedImageGenerator()
    generator.run_enhanced_system()

if __name__ == "__main__":
    main()
