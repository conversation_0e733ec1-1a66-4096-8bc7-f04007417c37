#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的DOM语义分割系统 - 建立精确的DOM像元到原始影像像素的对应关系
基于真实地理坐标和像素级映射，实现精确的标签传递
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 地理处理相关导入
try:
    import rasterio
    from rasterio.warp import transform
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio和pyproj可用，将使用高精度地理坐标转换")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio或pyproj不可用，无法进行地理坐标转换")

class PreciseDOMSegmentation:
    def __init__(self):
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.dom_bounds = None
        self.geographic_annotations = {}
        self.original_annotations = {}  # 原始像素坐标标注
        self.dom_segmentation = None
        self.transformer = None
        
        # 标签颜色映射
        self.label_colors = {
            'False Positive-Confusing (1)': 1,
            'Deciduous Vegetation (2)': 2,
            'Building': 3,
            'Road': 4,
            'Water': 5,
            'Vegetation': 6,
            'Background': 0
        }
        
        self.color_map = {
            0: (0, 0, 0),       # 背景 - 黑色
            1: (255, 0, 0),     # False Positive-Confusing - 红色
            2: (0, 255, 0),     # Deciduous Vegetation - 绿色
            3: (0, 0, 255),     # Building - 蓝色
            4: (255, 255, 0),   # Road - 黄色
            5: (0, 255, 255),   # Water - 青色
            6: (255, 0, 255),   # Vegetation - 洋红
        }
    
    def load_dom_data(self):
        """加载DOM数据和地理参考信息"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        if not RASTERIO_AVAILABLE:
            print("❌ 需要rasterio库进行地理坐标转换")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                self.dom_bounds = src.bounds
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM波段数: {src.count}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                print(f"   ✅ DOM像素分辨率: {self.dom_transform[0]:.6f} 米/像素")
                
                # 创建UTM到WGS84的坐标转换器
                self.transformer = Transformer.from_crs(self.dom_crs, 'EPSG:4326', always_xy=True)
                
                # 转换DOM边界到WGS84
                min_lon, min_lat = self.transformer.transform(self.dom_bounds.left, self.dom_bounds.bottom)
                max_lon, max_lat = self.transformer.transform(self.dom_bounds.right, self.dom_bounds.top)
                print(f"   ✅ DOM边界(WGS84): 经度 {min_lon:.6f} - {max_lon:.6f}, 纬度 {min_lat:.6f} - {max_lat:.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def load_annotations(self):
        """加载原始像素坐标标注数据和地理坐标标注数据"""
        print("📋 正在加载标注数据...")

        # 1. 加载原始像素坐标标注
        pixel_file = 'label.json'
        if os.path.exists(pixel_file):
            try:
                with open(pixel_file, 'r', encoding='utf-8') as f:
                    label_data = json.load(f)

                # 转换数据结构：从任务列表转换为图像名称到标注的映射
                self.original_annotations = {}
                for task in label_data:
                    if 'data' in task and 'image' in task['data']:
                        # 提取图像文件名
                        image_path = task['data']['image']
                        image_name = os.path.basename(image_path.split('?d=')[-1])

                        # 提取标注数据
                        annotations = []
                        if 'annotations' in task:
                            for annotation in task['annotations']:
                                if 'result' in annotation:
                                    for result in annotation['result']:
                                        if 'value' in result and 'points' in result['value']:
                                            # 转换为标准格式
                                            points = result['value']['points']
                                            # 将百分比坐标转换为像素坐标
                                            width = result.get('original_width', 5280)
                                            height = result.get('original_height', 3956)

                                            pixel_points = []
                                            for point in points:
                                                x = int((point[0] / 100.0) * width)
                                                y = int((point[1] / 100.0) * height)
                                                pixel_points.extend([x, y])

                                            # 获取标签
                                            label = 1  # 默认标签
                                            if 'polygonlabels' in result['value']:
                                                label_name = result['value']['polygonlabels'][0]
                                                label = self.label_colors.get(label_name, 1)

                                            annotations.append({
                                                'segmentation': [pixel_points],
                                                'category_id': label
                                            })

                        if annotations:
                            self.original_annotations[image_name] = annotations

                print(f"   ✅ 加载了 {len(self.original_annotations)} 张图像的像素标注数据")

                # 显示一些样本信息
                if self.original_annotations:
                    sample_image = list(self.original_annotations.keys())[0]
                    sample_annotations = self.original_annotations[sample_image]
                    print(f"   📋 样本图像 {sample_image} 有 {len(sample_annotations)} 个标注")

            except Exception as e:
                print(f"   ⚠️ 加载像素标注失败: {e}")
                import traceback
                traceback.print_exc()

        # 2. 加载地理坐标标注
        geo_file = 'geographic_annotations.json'
        if os.path.exists(geo_file):
            try:
                with open(geo_file, 'r', encoding='utf-8') as f:
                    geo_data = json.load(f)
                self.geographic_annotations = geo_data.get('annotations', {})
                print(f"   ✅ 加载了 {len(self.geographic_annotations)} 张图像的地理标注数据")

                if 'bounds' in geo_data:
                    bounds = geo_data['bounds']
                    print(f"   ✅ 标注边界: 经度 {bounds[0]:.6f} - {bounds[2]:.6f}, 纬度 {bounds[1]:.6f} - {bounds[3]:.6f}")

            except Exception as e:
                print(f"   ⚠️ 加载地理标注失败: {e}")

        return len(self.original_annotations) > 0 or len(self.geographic_annotations) > 0
    
    def create_image_segmentation_mask(self, image_name, width=5280, height=3956):
        """为指定图像创建分割掩码"""
        if image_name not in self.original_annotations:
            return np.zeros((height, width), dtype=np.uint8)
        
        mask = np.zeros((height, width), dtype=np.uint8)
        annotations = self.original_annotations[image_name]
        
        for annotation in annotations:
            if 'segmentation' in annotation:
                # 处理多边形分割
                segmentation = annotation['segmentation']
                if isinstance(segmentation, list) and len(segmentation) > 0:
                    # 转换为OpenCV格式的多边形点
                    if isinstance(segmentation[0], list):
                        # 多个多边形
                        for seg in segmentation:
                            if len(seg) >= 6:  # 至少3个点
                                points = np.array(seg).reshape(-1, 2).astype(np.int32)
                                cv2.fillPoly(mask, [points], annotation.get('category_id', 1))
                    else:
                        # 单个多边形
                        if len(segmentation) >= 6:
                            points = np.array(segmentation).reshape(-1, 2).astype(np.int32)
                            cv2.fillPoly(mask, [points], annotation.get('category_id', 1))
            
            elif 'bbox' in annotation:
                # 处理边界框
                bbox = annotation['bbox']
                x, y, w, h = bbox
                x, y, w, h = int(x), int(y), int(w), int(h)
                mask[y:y+h, x:x+w] = annotation.get('category_id', 1)
        
        return mask
    
    def get_image_geographic_bounds(self, image_name):
        """获取图像的地理边界"""
        if image_name not in self.geographic_annotations:
            return None
        
        annotations = self.geographic_annotations[image_name]
        all_lons, all_lats = [], []
        
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                for coord in coords:
                    all_lons.append(coord[0])
                    all_lats.append(coord[1])
        
        if not all_lons:
            return None
        
        return {
            'min_lon': min(all_lons),
            'max_lon': max(all_lons),
            'min_lat': min(all_lats),
            'max_lat': max(all_lats)
        }
    
    def point_in_polygon(self, x, y, polygon_coords):
        """使用射线法判断点是否在多边形内"""
        n = len(polygon_coords)
        inside = False
        
        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def get_label_from_geographic_point(self, lon, lat, image_name):
        """从地理坐标获取标签"""
        if image_name not in self.geographic_annotations:
            return 0
        
        annotations = self.geographic_annotations[image_name]
        
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                
                if self.point_in_polygon(lon, lat, coords):
                    # 尝试获取标签
                    if 'properties' in annotation:
                        for label_field in ['label', 'class', 'category', 'type']:
                            if label_field in annotation['properties']:
                                label_name = annotation['properties'][label_field]
                                return self.label_colors.get(label_name, 2)
                    return 2  # 默认植被标签
        
        return 0
    
    def build_precise_mapping(self):
        """建立精确的DOM像元到原始影像像素的映射关系"""
        print("🔗 正在建立精确的DOM-原始影像映射关系...")
        
        bands, dom_height, dom_width = self.dom_data.shape
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
            print(f"   🎭 有效像元: {np.sum(valid_mask):,} / {dom_height * dom_width:,}")
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        # 为每张原始影像创建分割掩码
        print("   🎨 正在为原始影像创建分割掩码...")
        image_masks = {}
        image_bounds = {}
        
        # 获取所有可用的图像名称
        all_images = set()
        if self.original_annotations:
            all_images.update(self.original_annotations.keys())
        if self.geographic_annotations:
            all_images.update(self.geographic_annotations.keys())
        
        for image_name in all_images:
            # 创建分割掩码
            if image_name in self.original_annotations:
                mask = self.create_image_segmentation_mask(image_name)
                image_masks[image_name] = mask
                unique_labels = np.unique(mask)
                print(f"      📸 {image_name}: 标签 {unique_labels}")
            
            # 获取地理边界
            bounds = self.get_image_geographic_bounds(image_name)
            if bounds:
                image_bounds[image_name] = bounds
        
        print(f"   ✅ 创建了 {len(image_masks)} 个分割掩码")
        print(f"   ✅ 获取了 {len(image_bounds)} 个地理边界")
        
        return image_masks, image_bounds, valid_mask

    def map_dom_to_images_precise(self):
        """精确映射DOM像元到原始影像像素"""
        print("🎯 正在进行精确的DOM到原始影像映射...")

        # 建立映射关系
        image_masks, image_bounds, valid_mask = self.build_precise_mapping()

        if not image_masks:
            print("❌ 没有可用的图像掩码")
            return False

        bands, dom_height, dom_width = self.dom_data.shape
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)

        # 使用适中的采样步长
        sample_step = 10
        processed_pixels = 0
        total_pixels = 0

        # 计算需要处理的像元数
        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                if valid_mask[row, col]:
                    total_pixels += 1

        print(f"   📊 预计处理 {total_pixels:,} 个采样点...")

        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                if not valid_mask[row, col]:
                    continue

                # 将DOM像元坐标转换为地理坐标
                utm_x, utm_y = self.dom_transform * (col, row)

                try:
                    geo_lon, geo_lat = self.transformer.transform(utm_x, utm_y)
                except:
                    continue

                # 收集标签投票
                labels_votes = {}

                # 方法1: 使用地理边界快速筛选可能的影像
                candidate_images = []
                for image_name, bounds in image_bounds.items():
                    if (bounds['min_lon'] <= geo_lon <= bounds['max_lon'] and
                        bounds['min_lat'] <= geo_lat <= bounds['max_lat']):
                        candidate_images.append(image_name)

                # 方法2: 对候选影像进行精确的像素级映射
                for image_name in candidate_images:
                    if image_name in image_masks:
                        # 从地理坐标获取标签（如果有地理标注）
                        if image_name in self.geographic_annotations:
                            label = self.get_label_from_geographic_point(geo_lon, geo_lat, image_name)
                            if label > 0:
                                labels_votes[label] = labels_votes.get(label, 0) + 1

                        # 或者使用像素掩码（如果有像素标注）
                        elif image_name in self.original_annotations:
                            # 这里需要建立地理坐标到像素坐标的映射
                            # 简化处理：假设影像覆盖整个区域，使用比例映射
                            bounds = image_bounds.get(image_name)
                            if bounds:
                                # 计算在影像中的相对位置
                                rel_x = (geo_lon - bounds['min_lon']) / (bounds['max_lon'] - bounds['min_lon'])
                                rel_y = (geo_lat - bounds['min_lat']) / (bounds['max_lat'] - bounds['min_lat'])

                                # 转换为像素坐标
                                pixel_x = int(rel_x * 5280)  # 假设影像宽度5280
                                pixel_y = int((1 - rel_y) * 3956)  # 假设影像高度3956，注意Y轴翻转

                                if 0 <= pixel_x < 5280 and 0 <= pixel_y < 3956:
                                    mask = image_masks[image_name]
                                    label = mask[pixel_y, pixel_x]
                                    if label > 0:
                                        labels_votes[label] = labels_votes.get(label, 0) + 1

                # 选择得票最多的标签
                if labels_votes:
                    final_label = max(labels_votes.keys(), key=lambda k: labels_votes[k])

                    # 填充邻域区域
                    for r in range(max(0, row-sample_step//2), min(dom_height, row+sample_step+sample_step//2)):
                        for c in range(max(0, col-sample_step//2), min(dom_width, col+sample_step+sample_step//2)):
                            if valid_mask[r, c] and self.dom_segmentation[r, c] == 0:
                                self.dom_segmentation[r, c] = final_label

                processed_pixels += 1
                if processed_pixels % 500 == 0:
                    progress = (processed_pixels / total_pixels) * 100
                    print(f"   📊 映射进度: {progress:.1f}% ({processed_pixels}/{total_pixels})")

        # 统计结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_dom_pixels = dom_height * dom_width

        print(f"   ✅ 精确映射完成")
        print(f"   📊 分割结果统计:")
        label_name_map = {v: k for k, v in self.label_colors.items()}
        for label, count in zip(unique_labels, counts):
            label_name = label_name_map.get(label, f"Unknown({label})")
            percentage = (count / total_dom_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")

        return True

    def save_results(self):
        """保存分割结果"""
        print("💾 正在保存分割结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码
        cv2.imwrite('precise_dom_segmentation_mask.png', self.dom_segmentation)

        # 创建彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        # 保存彩色分割结果
        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('precise_dom_segmentation_result.png', segmentation_bgr)

        # 创建叠加可视化
        if self.dom_data.shape[0] >= 3:
            dom_rgb = np.transpose(self.dom_data[:3], (1, 2, 0))
            dom_rgb = np.clip(dom_rgb, 0, 255).astype(np.uint8)
        else:
            dom_gray = self.dom_data[0]
            dom_rgb = np.stack([dom_gray, dom_gray, dom_gray], axis=2)
            dom_rgb = np.clip(dom_rgb, 0, 255).astype(np.uint8)

        # 创建叠加图像
        overlay = dom_rgb.copy().astype(np.float32)
        seg_mask = np.any(segmentation_rgb > 0, axis=2)

        overlay[seg_mask] = overlay[seg_mask] * 0.6 + segmentation_rgb[seg_mask].astype(np.float32) * 0.4
        overlay = np.clip(overlay, 0, 255).astype(np.uint8)

        overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('precise_dom_segmentation_overlay.png', overlay_bgr)

        print("   ✅ 分割掩码已保存: precise_dom_segmentation_mask.png")
        print("   ✅ 分割结果已保存: precise_dom_segmentation_result.png")
        print("   ✅ 叠加可视化已保存: precise_dom_segmentation_overlay.png")

    def run_precise_segmentation(self):
        """运行精确的DOM分割系统"""
        print("=" * 80)
        print("🎯 精确的DOM语义分割系统 - 建立精确的像素级对应关系")
        print("=" * 80)

        # 1. 加载DOM数据
        if not self.load_dom_data():
            return False

        # 2. 加载标注数据
        if not self.load_annotations():
            return False

        # 3. 执行精确映射
        if not self.map_dom_to_images_precise():
            return False

        # 4. 保存结果
        self.save_results()

        print("\n" + "=" * 80)
        print("🎯 精确的DOM语义分割完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • precise_dom_segmentation_result.png - 彩色分割结果")
        print("   • precise_dom_segmentation_mask.png - 灰度分割掩码")
        print("   • precise_dom_segmentation_overlay.png - DOM与分割结果叠加")

        print("\n🔧 精确映射的关键改进:")
        print("   ✅ 建立精确的DOM像元到原始影像像素的对应关系")
        print("   ✅ 使用地理边界快速筛选候选影像")
        print("   ✅ 实现地理坐标到像素坐标的精确转换")
        print("   ✅ 结合像素级标注和地理标注进行标签映射")
        print("   ✅ 改进的标签投票和决策机制")

        return True

def main():
    """主函数"""
    system = PreciseDOMSegmentation()
    success = system.run_precise_segmentation()

    if success:
        print("\n🎉 精确DOM分割任务成功完成！")
        print("📋 核心改进:")
        print("   ✅ 精确的DOM像元到原始影像像素映射")
        print("   ✅ 基于地理边界的候选影像筛选")
        print("   ✅ 地理坐标到像素坐标的精确转换")
        print("   ✅ 多源标注数据的融合利用")
        print("   ✅ 改进的标签投票和决策机制")
    else:
        print("\n❌ 精确DOM分割任务失败，请检查错误信息")

if __name__ == "__main__":
    main()
