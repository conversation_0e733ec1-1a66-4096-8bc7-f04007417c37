#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超精确映射系统 - 多策略融合的高精度标签映射
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import re
from datetime import datetime
import math

class UltraPreciseMapping:
    def __init__(self):
        self.image_label_masks = {}
        self.dom_data = None
        self.dom_shape = None
        self.dom_segmentation = None
        self.image_metadata = {}  # 存储每张图像的详细元数据
        self.flight_path = []     # 推断的飞行路径
        self.mapping_confidence = {}  # 每个映射的置信度
        
        # 高精度参数
        self.overlap_ratio = 0.6  # 图像重叠率
        self.flight_height = 120  # 估算飞行高度(米)
        self.camera_fov = 84      # 相机视场角(度)
        self.ground_resolution = 0.03  # 地面分辨率(米/像素)
        
    def load_dom_reference(self):
        """加载DOM参考数据"""
        print("🗺️ 加载DOM参考数据...")
        
        # 加载现有DOM掩码作为参考
        dom_mask_file = 'manual_dom_segmentation_mask.png'
        if os.path.exists(dom_mask_file):
            dom_mask = cv2.imread(dom_mask_file, cv2.IMREAD_GRAYSCALE)
            if dom_mask is not None:
                self.dom_shape = dom_mask.shape
                print(f"   ✅ DOM参考尺寸: {self.dom_shape}")
                
                # 分析现有DOM的分布模式
                house_regions = np.where(dom_mask == 1)
                if len(house_regions[0]) > 0:
                    # 计算房子区域的分布特征
                    min_row, max_row = np.min(house_regions[0]), np.max(house_regions[0])
                    min_col, max_col = np.min(house_regions[1]), np.max(house_regions[1])
                    
                    self.dom_bounds = {
                        'min_row': min_row, 'max_row': max_row,
                        'min_col': min_col, 'max_col': max_col,
                        'center_row': (min_row + max_row) // 2,
                        'center_col': (min_col + max_col) // 2
                    }
                    
                    print(f"   📍 DOM活动区域: 行{min_row}-{max_row}, 列{min_col}-{max_col}")
                    return True
        
        # 如果没有参考，创建默认尺寸
        self.dom_shape = (12000, 12000)  # 默认大尺寸
        self.dom_bounds = {
            'min_row': 1000, 'max_row': 11000,
            'min_col': 1000, 'max_col': 11000,
            'center_row': 6000, 'center_col': 6000
        }
        print(f"   ✅ 使用默认DOM尺寸: {self.dom_shape}")
        return True
    
    def load_and_analyze_images(self):
        """加载并深度分析图像"""
        print("🔍 深度分析图像数据...")
        
        # 加载标签掩码
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 标签统计文件不存在")
            return False
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        for image_name, stats in stats_data.items():
            mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
            
            if os.path.exists(mask_file):
                mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    # 深度分析图像
                    metadata = self.extract_deep_metadata(image_name, mask, stats)
                    
                    self.image_label_masks[image_name] = {
                        'mask': mask,
                        'stats': stats,
                        'metadata': metadata
                    }
                    
                    self.image_metadata[image_name] = metadata
        
        print(f"   ✅ 深度分析了 {len(self.image_metadata)} 个图像")
        return len(self.image_metadata) > 0
    
    def extract_deep_metadata(self, image_name, mask, stats):
        """提取图像的深度元数据"""
        metadata = {}
        
        # 1. 从文件名提取时间信息
        time_match = re.search(r'(\d{8})(\d{6})', image_name)
        if time_match:
            date_str, time_str = time_match.groups()
            try:
                dt = datetime.strptime(f"{date_str}{time_str}", "%Y%m%d%H%M%S")
                metadata['datetime'] = dt
                metadata['timestamp'] = dt.timestamp()
                metadata['time_str'] = time_str
            except:
                metadata['timestamp'] = 0
        
        # 2. 从文件名提取序号
        seq_match = re.search(r'_(\d+)_?V?\.JPG', image_name)
        if seq_match:
            metadata['sequence'] = int(seq_match.group(1))
        else:
            metadata['sequence'] = 0
        
        # 3. 分析标签分布特征
        house_pixels = mask == 1
        if np.any(house_pixels):
            house_coords = np.where(house_pixels)
            
            # 计算房子区域的几何特征
            metadata['house_centroid'] = (
                np.mean(house_coords[1]),  # x坐标
                np.mean(house_coords[0])   # y坐标
            )
            
            metadata['house_bbox'] = (
                np.min(house_coords[1]), np.min(house_coords[0]),  # min_x, min_y
                np.max(house_coords[1]), np.max(house_coords[0])   # max_x, max_y
            )
            
            # 计算房子区域的分散度
            metadata['house_spread'] = np.std(house_coords[0]) + np.std(house_coords[1])
            
            # 计算房子区域的密度
            bbox_area = (metadata['house_bbox'][2] - metadata['house_bbox'][0]) * \
                       (metadata['house_bbox'][3] - metadata['house_bbox'][1])
            metadata['house_density'] = stats['house_pixels'] / max(bbox_area, 1)
        else:
            metadata['house_centroid'] = (mask.shape[1]//2, mask.shape[0]//2)
            metadata['house_bbox'] = (0, 0, mask.shape[1], mask.shape[0])
            metadata['house_spread'] = 0
            metadata['house_density'] = 0
        
        # 4. 计算图像特征向量（用于相似度匹配）
        metadata['feature_vector'] = [
            stats['house_percentage'],
            metadata['house_spread'],
            metadata['house_density'],
            metadata['house_centroid'][0] / mask.shape[1],  # 归一化x
            metadata['house_centroid'][1] / mask.shape[0]   # 归一化y
        ]
        
        return metadata
    
    def infer_flight_path(self):
        """推断飞行路径"""
        print("✈️ 推断飞行路径...")
        
        # 按时间戳排序
        sorted_images = sorted(self.image_metadata.items(), 
                             key=lambda x: x[1]['timestamp'])
        
        # 分析飞行模式
        flight_patterns = []
        
        # 模式1: 时间序列分析
        time_based_path = self.analyze_time_pattern(sorted_images)
        flight_patterns.append(('time_based', time_based_path, 0.7))
        
        # 模式2: 序号分析
        sequence_based_path = self.analyze_sequence_pattern(sorted_images)
        flight_patterns.append(('sequence_based', sequence_based_path, 0.8))
        
        # 模式3: 内容相似度分析
        content_based_path = self.analyze_content_similarity(sorted_images)
        flight_patterns.append(('content_based', content_based_path, 0.9))
        
        # 融合多种模式
        self.flight_path = self.fuse_flight_patterns(flight_patterns)
        
        print(f"   ✅ 推断出飞行路径，包含 {len(self.flight_path)} 个位置")
        return True
    
    def analyze_time_pattern(self, sorted_images):
        """基于时间模式分析"""
        path = []
        
        for i, (image_name, metadata) in enumerate(sorted_images):
            # 基于时间间隔推断位置
            time_factor = i / max(len(sorted_images) - 1, 1)
            
            # 假设按S型路径飞行
            if i % 2 == 0:  # 偶数行，从左到右
                x_progress = time_factor % (1.0 / 6)  # 6列
                y_progress = time_factor // (1.0 / 6)
            else:  # 奇数行，从右到左
                x_progress = 1 - (time_factor % (1.0 / 6))
                y_progress = time_factor // (1.0 / 6)
            
            path.append({
                'image_name': image_name,
                'x_progress': x_progress * 6,  # 转换为网格坐标
                'y_progress': y_progress * 6,
                'confidence': 0.7
            })
        
        return path
    
    def analyze_sequence_pattern(self, sorted_images):
        """基于序号模式分析"""
        path = []
        
        # 按序号重新排序
        seq_sorted = sorted(sorted_images, key=lambda x: x[1]['sequence'])
        
        for i, (image_name, metadata) in enumerate(seq_sorted):
            # 基于序号的网格分布
            grid_size = int(np.ceil(np.sqrt(len(seq_sorted))))
            
            row = i // grid_size
            col = i % grid_size
            
            path.append({
                'image_name': image_name,
                'x_progress': col,
                'y_progress': row,
                'confidence': 0.8
            })
        
        return path
    
    def analyze_content_similarity(self, sorted_images):
        """基于内容相似度分析"""
        path = []
        
        # 计算所有图像间的相似度
        similarities = {}
        
        for i, (name1, meta1) in enumerate(sorted_images):
            similarities[name1] = {}
            for j, (name2, meta2) in enumerate(sorted_images):
                if i != j:
                    # 计算特征向量的相似度
                    sim = self.calculate_similarity(meta1['feature_vector'], 
                                                  meta2['feature_vector'])
                    similarities[name1][name2] = sim
        
        # 基于相似度构建邻接关系
        visited = set()
        current = sorted_images[0][0]  # 从第一张图像开始
        
        grid_size = int(np.ceil(np.sqrt(len(sorted_images))))
        position_map = {}
        
        for i in range(len(sorted_images)):
            if current not in visited:
                visited.add(current)
                
                # 分配网格位置
                row = i // grid_size
                col = i % grid_size
                
                position_map[current] = (col, row)
                
                path.append({
                    'image_name': current,
                    'x_progress': col,
                    'y_progress': row,
                    'confidence': 0.9
                })
                
                # 找到最相似的未访问图像
                if current in similarities:
                    best_next = None
                    best_sim = -1
                    
                    for next_name, sim in similarities[current].items():
                        if next_name not in visited and sim > best_sim:
                            best_sim = sim
                            best_next = next_name
                    
                    if best_next:
                        current = best_next
                    else:
                        # 如果没有找到，选择下一个未访问的
                        for name, _ in sorted_images:
                            if name not in visited:
                                current = name
                                break
        
        return path
    
    def calculate_similarity(self, vec1, vec2):
        """计算特征向量相似度"""
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        # 使用余弦相似度
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0
        
        return dot_product / (norm1 * norm2)
    
    def fuse_flight_patterns(self, patterns):
        """融合多种飞行模式"""
        print("   🔄 融合多种飞行模式...")
        
        # 为每个图像计算加权平均位置
        fused_path = []
        
        # 获取所有图像名称
        all_images = set()
        for pattern_name, path, weight in patterns:
            for pos in path:
                all_images.add(pos['image_name'])
        
        for image_name in all_images:
            weighted_x = 0
            weighted_y = 0
            total_weight = 0
            max_confidence = 0
            
            # 从每个模式中获取该图像的位置
            for pattern_name, path, pattern_weight in patterns:
                for pos in path:
                    if pos['image_name'] == image_name:
                        weight = pattern_weight * pos['confidence']
                        weighted_x += pos['x_progress'] * weight
                        weighted_y += pos['y_progress'] * weight
                        total_weight += weight
                        max_confidence = max(max_confidence, pos['confidence'])
                        break
            
            if total_weight > 0:
                fused_path.append({
                    'image_name': image_name,
                    'x_progress': weighted_x / total_weight,
                    'y_progress': weighted_y / total_weight,
                    'confidence': max_confidence
                })
        
        # 按置信度排序
        fused_path.sort(key=lambda x: x['confidence'], reverse=True)
        
        return fused_path

    def create_ultra_precise_mapping(self):
        """创建超精确映射"""
        print("🎯 创建超精确映射...")

        dom_height, dom_width = self.dom_shape
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)

        # 计算实际映射区域（基于DOM边界）
        active_width = self.dom_bounds['max_col'] - self.dom_bounds['min_col']
        active_height = self.dom_bounds['max_row'] - self.dom_bounds['min_row']

        print(f"   📐 活动映射区域: {active_width} x {active_height}")

        total_mapped_pixels = 0
        high_confidence_mappings = 0

        for i, position in enumerate(self.flight_path):
            image_name = position['image_name']

            if image_name not in self.image_label_masks:
                continue

            print(f"   🎯 超精确映射 {i+1}/{len(self.flight_path)}: {image_name}")

            # 计算精确位置
            precise_x, precise_y = self.calculate_precise_position(position, active_width, active_height)

            # 计算图像在DOM中的覆盖范围
            coverage = self.calculate_image_coverage(image_name, precise_x, precise_y)

            # 执行高精度映射
            mapped_pixels = self.execute_precise_mapping(image_name, coverage)

            total_mapped_pixels += mapped_pixels

            if position['confidence'] > 0.8:
                high_confidence_mappings += 1

            confidence_str = "🔥高" if position['confidence'] > 0.8 else "⚡中" if position['confidence'] > 0.6 else "💫低"
            print(f"      ✅ 位置({precise_x:.1f}, {precise_y:.1f}), 映射{mapped_pixels:,}像素, 置信度{confidence_str}({position['confidence']:.2f})")

        # 后处理优化
        self.post_process_optimization()

        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_pixels = dom_height * dom_width

        print(f"\n   📊 超精确映射统计:")
        for label, count in zip(unique_labels, counts):
            label_name = 'Background' if label == 0 else 'House'
            percentage = (count / total_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")

        print(f"   🎯 总映射像素: {total_mapped_pixels:,}")
        print(f"   🔥 高置信度映射: {high_confidence_mappings}/{len(self.flight_path)} ({high_confidence_mappings/len(self.flight_path)*100:.1f}%)")

        return True

    def calculate_precise_position(self, position, active_width, active_height):
        """计算精确位置"""
        # 基础网格位置
        base_x = self.dom_bounds['min_col'] + (position['x_progress'] / 6.0) * active_width
        base_y = self.dom_bounds['min_row'] + (position['y_progress'] / 6.0) * active_height

        # 添加基于图像内容的微调
        image_name = position['image_name']
        if image_name in self.image_metadata:
            metadata = self.image_metadata[image_name]

            # 基于房子重心的微调
            centroid_x_ratio = metadata['house_centroid'][0] / 5280  # 图像宽度
            centroid_y_ratio = metadata['house_centroid'][1] / 3956  # 图像高度

            # 微调范围（最多偏移一个网格单元的20%）
            adjust_range_x = active_width / 6.0 * 0.2
            adjust_range_y = active_height / 6.0 * 0.2

            # 应用微调
            adjust_x = (centroid_x_ratio - 0.5) * adjust_range_x
            adjust_y = (centroid_y_ratio - 0.5) * adjust_range_y

            precise_x = base_x + adjust_x
            precise_y = base_y + adjust_y
        else:
            precise_x = base_x
            precise_y = base_y

        return precise_x, precise_y

    def calculate_image_coverage(self, image_name, center_x, center_y):
        """计算图像覆盖范围"""
        # 基于飞行高度和相机参数计算地面覆盖范围
        ground_width = 2 * self.flight_height * math.tan(math.radians(self.camera_fov / 2))
        ground_height = ground_width * (3956 / 5280)  # 基于图像宽高比

        # 转换为DOM像素
        pixel_width = ground_width / self.ground_resolution
        pixel_height = ground_height / self.ground_resolution

        # 考虑重叠
        effective_width = pixel_width * (1 - self.overlap_ratio)
        effective_height = pixel_height * (1 - self.overlap_ratio)

        coverage = {
            'center_x': center_x,
            'center_y': center_y,
            'width': effective_width,
            'height': effective_height,
            'start_x': center_x - effective_width / 2,
            'end_x': center_x + effective_width / 2,
            'start_y': center_y - effective_height / 2,
            'end_y': center_y + effective_height / 2
        }

        return coverage

    def execute_precise_mapping(self, image_name, coverage):
        """执行精确映射"""
        mask_data = self.image_label_masks[image_name]
        label_mask = mask_data['mask']

        # 确保在DOM范围内
        dom_height, dom_width = self.dom_shape

        start_x = max(0, int(coverage['start_x']))
        end_x = min(dom_width, int(coverage['end_x']))
        start_y = max(0, int(coverage['start_y']))
        end_y = min(dom_height, int(coverage['end_y']))

        actual_width = end_x - start_x
        actual_height = end_y - start_y

        if actual_width <= 0 or actual_height <= 0:
            return 0

        # 高精度缩放
        scaled_mask = cv2.resize(label_mask, (actual_width, actual_height),
                               interpolation=cv2.INTER_CUBIC)

        # 应用阈值确保二值化
        scaled_mask = (scaled_mask > 127).astype(np.uint8)

        # 获取DOM区域
        dom_region = self.dom_segmentation[start_y:end_y, start_x:end_x]

        # 智能融合策略
        house_pixels = scaled_mask == 1

        # 如果有重叠，使用加权融合
        overlap_mask = (dom_region == 1) & house_pixels
        if np.any(overlap_mask):
            # 重叠区域保持原有标签
            new_pixels = house_pixels & (dom_region == 0)
        else:
            new_pixels = house_pixels

        dom_region[new_pixels] = 1

        return np.sum(new_pixels)

    def post_process_optimization(self):
        """后处理优化"""
        print("   🔧 执行后处理优化...")

        # 1. 形态学操作去除噪点
        kernel = np.ones((3, 3), np.uint8)

        # 闭运算填充小洞
        self.dom_segmentation = cv2.morphologyEx(self.dom_segmentation, cv2.MORPH_CLOSE, kernel)

        # 开运算去除小噪点
        self.dom_segmentation = cv2.morphologyEx(self.dom_segmentation, cv2.MORPH_OPEN, kernel)

        # 2. 连通域分析，去除过小的区域
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(self.dom_segmentation, connectivity=8)

        min_area = 100  # 最小区域面积
        for i in range(1, num_labels):  # 跳过背景
            if stats[i, cv2.CC_STAT_AREA] < min_area:
                self.dom_segmentation[labels == i] = 0

        print("      ✅ 形态学优化完成")
        print("      ✅ 连通域过滤完成")

    def save_ultra_precise_results(self):
        """保存超精确结果"""
        print("💾 保存超精确映射结果...")

        # 保存分割掩码
        cv2.imwrite('ultra_precise_mapping_mask.png', self.dom_segmentation)

        # 创建高对比度彩色图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        # 使用鲜明颜色
        color_map = {
            0: (0, 0, 0),         # 背景 - 黑色
            1: (255, 255, 0),     # 房子 - 亮黄色
        }

        for label_id, color in color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('ultra_precise_mapping_result.png', segmentation_bgr)

        # 创建多尺寸版本
        scales = [0.05, 0.1, 0.2, 0.5]
        for scale in scales:
            small_height = int(dom_height * scale)
            small_width = int(dom_width * scale)

            small_result = cv2.resize(segmentation_bgr, (small_width, small_height),
                                    interpolation=cv2.INTER_NEAREST)
            cv2.imwrite(f'ultra_precise_mapping_{int(scale*100)}percent.png', small_result)
            print(f"   ✅ {int(scale*100)}%版本: ultra_precise_mapping_{int(scale*100)}percent.png ({small_width}x{small_height})")

        # 保存详细信息
        mapping_info = {
            'dom_shape': [int(x) for x in self.dom_shape],
            'dom_bounds': {k: int(v) for k, v in self.dom_bounds.items()},
            'num_images': int(len(self.image_label_masks)),
            'flight_path_length': int(len(self.flight_path)),
            'total_house_pixels': int(np.sum(self.dom_segmentation == 1)),
            'house_percentage': float(np.sum(self.dom_segmentation == 1) / (dom_height * dom_width) * 100),
            'mapping_parameters': {
                'overlap_ratio': float(self.overlap_ratio),
                'flight_height': int(self.flight_height),
                'camera_fov': int(self.camera_fov),
                'ground_resolution': float(self.ground_resolution)
            },
            'confidence_distribution': {
                'high': int(sum(1 for p in self.flight_path if p['confidence'] > 0.8)),
                'medium': int(sum(1 for p in self.flight_path if 0.6 < p['confidence'] <= 0.8)),
                'low': int(sum(1 for p in self.flight_path if p['confidence'] <= 0.6))
            }
        }

        with open('ultra_precise_mapping_info.json', 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)

        print("   ✅ 超精确映射结果已保存: ultra_precise_mapping_result.png")
        print("   ✅ 超精确映射掩码已保存: ultra_precise_mapping_mask.png")
        print("   ✅ 详细信息已保存: ultra_precise_mapping_info.json")

    def run_ultra_precise_mapping(self):
        """运行超精确映射系统"""
        print("=" * 80)
        print("🚀 超精确映射系统 - 多策略融合高精度版本")
        print("=" * 80)

        # 1. 加载DOM参考
        if not self.load_dom_reference():
            return False

        # 2. 深度分析图像
        if not self.load_and_analyze_images():
            return False

        # 3. 推断飞行路径
        if not self.infer_flight_path():
            return False

        # 4. 创建超精确映射
        if not self.create_ultra_precise_mapping():
            return False

        # 5. 保存结果
        self.save_ultra_precise_results()

        print("\n" + "=" * 80)
        print("🚀 超精确映射完成！")
        print("=" * 80)
        print("📁 生成的超精确文件:")
        print("   • ultra_precise_mapping_result.png - 超精确映射结果")
        print("   • ultra_precise_mapping_5percent.png - 5%版本 ⭐推荐查看")
        print("   • ultra_precise_mapping_10percent.png - 10%版本")
        print("   • ultra_precise_mapping_20percent.png - 20%版本")
        print("   • ultra_precise_mapping_50percent.png - 50%版本")
        print("   • ultra_precise_mapping_mask.png - 超精确掩码")
        print("   • ultra_precise_mapping_info.json - 详细信息")

        print("\n🚀 超精确系统特点:")
        print("   🎯 多策略飞行路径推断 (时间+序号+内容相似度)")
        print("   📐 基于相机参数的精确覆盖计算")
        print("   🔧 图像内容驱动的位置微调")
        print("   🎨 智能重叠区域融合")
        print("   ✨ 形态学后处理优化")
        print("   📊 置信度评估和质量控制")

        return True

def main():
    """主函数"""
    system = UltraPreciseMapping()
    success = system.run_ultra_precise_mapping()

    if success:
        print("\n🎉 超精确映射成功！")
        print("📋 精度提升策略:")
        print("   🎯 多模式飞行路径融合")
        print("   📐 相机参数精确计算")
        print("   🔧 内容驱动位置微调")
        print("   🎨 智能重叠处理")
        print("   ✨ 后处理优化")
        print("\n💡 建议查看: ultra_precise_mapping_5percent.png")
    else:
        print("\n❌ 超精确映射失败")

if __name__ == "__main__":
    main()
