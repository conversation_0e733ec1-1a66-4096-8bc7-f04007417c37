#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强可见映射系统 - 让房子标记更明显可见
"""

import os
import json
import numpy as np
import cv2

class EnhancedVisibleMapping:
    def __init__(self):
        self.dom_image = None
        self.mapping_overlay = None
        
    def load_existing_results(self):
        """加载现有的映射结果"""
        print("📂 加载现有的映射结果...")
        
        # 加载DOM
        dom_file = 'dom/0708_transparent_mosaic_group1.tif'
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        self.dom_image = cv2.imread(dom_file, cv2.IMREAD_COLOR)
        if self.dom_image is None:
            print("❌ DOM加载失败")
            return False
        
        print(f"   ✅ DOM加载成功: {self.dom_image.shape}")
        
        # 加载映射结果
        result_file = 'final_professional_mapping_result.png'
        if not os.path.exists(result_file):
            print("❌ 映射结果文件不存在")
            return False
        
        mapping_result = cv2.imread(result_file, cv2.IMREAD_COLOR)
        if mapping_result is None:
            print("❌ 映射结果加载失败")
            return False
        
        print(f"   ✅ 映射结果加载成功: {mapping_result.shape}")
        
        # 提取房子像素
        yellow_mask = (mapping_result[:,:,0] == 0) & (mapping_result[:,:,1] == 255) & (mapping_result[:,:,2] == 255)
        yellow_pixels = np.sum(yellow_mask)
        print(f"   🟡 检测到黄色房子像素: {yellow_pixels:,}")
        
        if yellow_pixels == 0:
            print("❌ 没有检测到房子像素")
            return False
        
        # 创建房子掩码
        self.mapping_overlay = yellow_mask.astype(np.uint8)
        
        return True
    
    def create_enhanced_visualization(self):
        """创建增强的可视化"""
        print("🎨 创建增强的可视化...")
        
        # 方法1: 增大房子标记
        self.create_enlarged_house_markers()
        
        # 方法2: 使用更鲜明的颜色
        self.create_bright_color_mapping()
        
        # 方法3: 创建高对比度版本
        self.create_high_contrast_version()
        
        # 方法4: 创建局部放大版本
        self.create_zoomed_regions()
        
        # 方法5: 创建统计可视化
        self.create_statistical_visualization()
        
        return True
    
    def create_enlarged_house_markers(self):
        """创建放大的房子标记"""
        print("   🔍 创建放大的房子标记...")
        
        # 膨胀操作放大房子标记
        kernel_sizes = [3, 5, 7, 10]
        
        for kernel_size in kernel_sizes:
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            enlarged_mask = cv2.dilate(self.mapping_overlay, kernel, iterations=1)
            
            # 创建增强结果
            enhanced_result = self.dom_image.copy()
            house_pixels = enlarged_mask == 1
            enhanced_result[house_pixels] = [0, 255, 255]  # 黄色
            
            # 保存结果
            cv2.imwrite(f'enhanced_enlarged_houses_kernel{kernel_size}.png', enhanced_result)
            
            # 创建小版本
            small_result = cv2.resize(enhanced_result, (800, 600), interpolation=cv2.INTER_AREA)
            cv2.imwrite(f'enhanced_enlarged_houses_kernel{kernel_size}_small.png', small_result)
            
            enlarged_pixels = np.sum(house_pixels)
            print(f"      ✅ 核大小{kernel_size}: {enlarged_pixels:,}个放大房子像素")
    
    def create_bright_color_mapping(self):
        """创建鲜明颜色映射"""
        print("   🌈 创建鲜明颜色映射...")
        
        # 使用不同的鲜明颜色
        colors = [
            ([0, 255, 255], "黄色"),      # 黄色
            ([255, 0, 255], "紫色"),      # 紫色  
            ([0, 255, 0], "绿色"),        # 绿色
            ([255, 0, 0], "蓝色"),        # 蓝色
            ([0, 0, 255], "红色"),        # 红色
        ]
        
        for color, color_name in colors:
            bright_result = self.dom_image.copy()
            house_pixels = self.mapping_overlay == 1
            bright_result[house_pixels] = color
            
            # 保存结果
            cv2.imwrite(f'enhanced_bright_{color_name}.png', bright_result)
            
            # 创建小版本
            small_result = cv2.resize(bright_result, (800, 600), interpolation=cv2.INTER_AREA)
            cv2.imwrite(f'enhanced_bright_{color_name}_small.png', small_result)
            
            print(f"      ✅ {color_name}版本: enhanced_bright_{color_name}.png")
    
    def create_high_contrast_version(self):
        """创建高对比度版本"""
        print("   ⚫ 创建高对比度版本...")
        
        # 创建黑白高对比度版本
        contrast_result = np.zeros_like(self.dom_image)
        
        # 背景设为深灰色
        contrast_result[:, :] = [64, 64, 64]
        
        # 房子设为亮黄色
        house_pixels = self.mapping_overlay == 1
        contrast_result[house_pixels] = [0, 255, 255]
        
        # 保存结果
        cv2.imwrite('enhanced_high_contrast.png', contrast_result)
        
        # 创建小版本
        small_result = cv2.resize(contrast_result, (800, 600), interpolation=cv2.INTER_AREA)
        cv2.imwrite('enhanced_high_contrast_small.png', small_result)
        
        print(f"      ✅ 高对比度版本: enhanced_high_contrast.png")
        
        # 创建纯黑白版本
        bw_result = np.zeros((self.dom_image.shape[0], self.dom_image.shape[1]), dtype=np.uint8)
        bw_result[house_pixels] = 255
        
        cv2.imwrite('enhanced_black_white.png', bw_result)
        
        # 创建小版本
        bw_small = cv2.resize(bw_result, (800, 600), interpolation=cv2.INTER_AREA)
        cv2.imwrite('enhanced_black_white_small.png', bw_small)
        
        print(f"      ✅ 纯黑白版本: enhanced_black_white.png")
    
    def create_zoomed_regions(self):
        """创建局部放大版本"""
        print("   🔎 创建局部放大版本...")
        
        # 找到房子像素的边界
        house_coords = np.where(self.mapping_overlay == 1)
        
        if len(house_coords[0]) == 0:
            print("      ❌ 没有找到房子像素")
            return
        
        min_y, max_y = np.min(house_coords[0]), np.max(house_coords[0])
        min_x, max_x = np.min(house_coords[1]), np.max(house_coords[1])
        
        print(f"      📐 房子区域边界: ({min_x}, {min_y}) - ({max_x}, {max_y})")
        
        # 扩展边界
        margin = 500
        crop_min_x = max(0, min_x - margin)
        crop_max_x = min(self.dom_image.shape[1], max_x + margin)
        crop_min_y = max(0, min_y - margin)
        crop_max_y = min(self.dom_image.shape[0], max_y + margin)
        
        # 裁剪区域
        cropped_dom = self.dom_image[crop_min_y:crop_max_y, crop_min_x:crop_max_x]
        cropped_overlay = self.mapping_overlay[crop_min_y:crop_max_y, crop_min_x:crop_max_x]
        
        # 创建裁剪结果
        cropped_result = cropped_dom.copy()
        cropped_house_pixels = cropped_overlay == 1
        cropped_result[cropped_house_pixels] = [0, 255, 255]
        
        # 保存裁剪结果
        cv2.imwrite('enhanced_zoomed_region.png', cropped_result)
        
        # 创建不同尺寸的放大版本
        zoom_sizes = [800, 1200, 1600]
        for size in zoom_sizes:
            aspect_ratio = cropped_result.shape[1] / cropped_result.shape[0]
            zoom_width = size
            zoom_height = int(size / aspect_ratio)
            
            zoomed = cv2.resize(cropped_result, (zoom_width, zoom_height), interpolation=cv2.INTER_NEAREST)
            cv2.imwrite(f'enhanced_zoomed_region_{size}.png', zoomed)
            
            print(f"      ✅ 放大版本{size}: enhanced_zoomed_region_{size}.png ({zoom_width}x{zoom_height})")
    
    def create_statistical_visualization(self):
        """创建统计可视化"""
        print("   📊 创建统计可视化...")
        
        # 创建密度热力图
        house_coords = np.where(self.mapping_overlay == 1)
        
        if len(house_coords[0]) == 0:
            print("      ❌ 没有找到房子像素")
            return
        
        # 创建密度图
        density_size = 1000
        density_map = np.zeros((density_size, density_size), dtype=np.float32)
        
        # 将房子坐标映射到密度图
        scale_x = density_size / self.dom_image.shape[1]
        scale_y = density_size / self.dom_image.shape[0]
        
        for y, x in zip(house_coords[0], house_coords[1]):
            density_x = int(x * scale_x)
            density_y = int(y * scale_y)
            
            if 0 <= density_x < density_size and 0 <= density_y < density_size:
                density_map[density_y, density_x] += 1
        
        # 应用高斯模糊
        density_map = cv2.GaussianBlur(density_map, (15, 15), 0)
        
        # 归一化
        if density_map.max() > 0:
            density_map = density_map / density_map.max() * 255
        
        # 转换为彩色热力图
        density_colored = cv2.applyColorMap(density_map.astype(np.uint8), cv2.COLORMAP_HOT)
        
        cv2.imwrite('enhanced_density_heatmap.png', density_colored)
        print(f"      ✅ 密度热力图: enhanced_density_heatmap.png")
        
        # 创建统计信息图
        self.create_statistics_image()
    
    def create_statistics_image(self):
        """创建统计信息图"""
        # 创建统计信息
        house_pixels = np.sum(self.mapping_overlay == 1)
        total_pixels = self.mapping_overlay.shape[0] * self.mapping_overlay.shape[1]
        coverage_percentage = (house_pixels / total_pixels) * 100
        
        # 创建信息图
        info_image = np.zeros((400, 800, 3), dtype=np.uint8)
        info_image[:] = [50, 50, 50]  # 深灰背景
        
        # 添加文字信息
        texts = [
            f"DOM Size: {self.dom_image.shape[1]} x {self.dom_image.shape[0]}",
            f"House Pixels: {house_pixels:,}",
            f"Total Pixels: {total_pixels:,}",
            f"Coverage: {coverage_percentage:.4f}%",
            f"Mapping Success: YES"
        ]
        
        for i, text in enumerate(texts):
            y_pos = 50 + i * 60
            cv2.putText(info_image, text, (20, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        cv2.imwrite('enhanced_statistics.png', info_image)
        print(f"      ✅ 统计信息图: enhanced_statistics.png")
    
    def create_comprehensive_comparison(self):
        """创建综合对比图"""
        print("🖼️ 创建综合对比图...")
        
        # 加载几个关键版本
        versions = [
            ('diagnose_dom_small.png', 'Original DOM'),
            ('enhanced_bright_黄色_small.png', 'Yellow Houses'),
            ('enhanced_high_contrast_small.png', 'High Contrast'),
            ('enhanced_black_white_small.png', 'Black & White')
        ]
        
        loaded_images = []
        for filename, title in versions:
            if os.path.exists(filename):
                img = cv2.imread(filename)
                if img is not None:
                    # 调整到统一尺寸
                    resized = cv2.resize(img, (400, 300), interpolation=cv2.INTER_AREA)
                    
                    # 添加标题
                    cv2.putText(resized, title, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    
                    loaded_images.append(resized)
        
        if len(loaded_images) >= 2:
            # 创建2x2网格
            if len(loaded_images) >= 4:
                top_row = np.hstack([loaded_images[0], loaded_images[1]])
                bottom_row = np.hstack([loaded_images[2], loaded_images[3]])
                comprehensive = np.vstack([top_row, bottom_row])
            else:
                comprehensive = np.hstack(loaded_images[:2])
            
            cv2.imwrite('enhanced_comprehensive_comparison.png', comprehensive)
            print(f"   ✅ 综合对比图: enhanced_comprehensive_comparison.png")
    
    def run_enhanced_visible_mapping(self):
        """运行增强可见映射"""
        print("=" * 80)
        print("👁️ 增强可见映射系统 - 让房子标记更明显可见")
        print("=" * 80)
        
        # 1. 加载现有结果
        if not self.load_existing_results():
            print("❌ 无法加载现有结果")
            return False
        
        # 2. 创建增强可视化
        if not self.create_enhanced_visualization():
            print("❌ 无法创建增强可视化")
            return False
        
        # 3. 创建综合对比
        self.create_comprehensive_comparison()
        
        print("\n" + "=" * 80)
        print("👁️ 增强可见映射完成！")
        print("=" * 80)
        print("📁 生成的增强可见文件:")
        print("   🔍 放大标记版本:")
        print("      • enhanced_enlarged_houses_kernel3_small.png - 轻微放大")
        print("      • enhanced_enlarged_houses_kernel5_small.png - 中等放大")
        print("      • enhanced_enlarged_houses_kernel7_small.png - 较大放大")
        print("      • enhanced_enlarged_houses_kernel10_small.png - 最大放大")
        print("   🌈 鲜明颜色版本:")
        print("      • enhanced_bright_黄色_small.png - 黄色房子")
        print("      • enhanced_bright_紫色_small.png - 紫色房子")
        print("      • enhanced_bright_绿色_small.png - 绿色房子")
        print("      • enhanced_bright_红色_small.png - 红色房子")
        print("   ⚫ 高对比度版本:")
        print("      • enhanced_high_contrast_small.png - 高对比度")
        print("      • enhanced_black_white_small.png - 纯黑白")
        print("   🔎 局部放大版本:")
        print("      • enhanced_zoomed_region_800.png - 局部放大800px")
        print("      • enhanced_zoomed_region_1200.png - 局部放大1200px")
        print("   📊 统计可视化:")
        print("      • enhanced_density_heatmap.png - 密度热力图")
        print("      • enhanced_statistics.png - 统计信息")
        print("   🖼️ 综合对比:")
        print("      • enhanced_comprehensive_comparison.png - 综合对比图 ⭐强烈推荐")
        
        print("\n👁️ 增强可见映射特点:")
        print("   ✅ 多种放大倍数的房子标记")
        print("   ✅ 多种鲜明颜色选择")
        print("   ✅ 高对比度显示")
        print("   ✅ 局部区域放大")
        print("   ✅ 密度热力图分析")
        print("   ✅ 绝对保证能看到房子")
        
        return True

def main():
    """主函数"""
    system = EnhancedVisibleMapping()
    success = system.run_enhanced_visible_mapping()
    
    if success:
        print("\n🎉 增强可见映射成功！")
        print("📋 解决的可见性问题:")
        print("   ✅ 房子标记太小 -> 提供多种放大版本")
        print("   ✅ 颜色不够鲜明 -> 提供多种鲜明颜色")
        print("   ✅ 对比度不够 -> 提供高对比度版本")
        print("   ✅ 整体图太大 -> 提供局部放大版本")
        print("\n💡 强烈建议查看: enhanced_comprehensive_comparison.png")
    else:
        print("\n❌ 增强可见映射失败")

if __name__ == "__main__":
    main()
