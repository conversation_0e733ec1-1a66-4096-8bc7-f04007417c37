#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终工作映射系统 - 基于成功测试的确保工作版本
"""

import os
import json
import numpy as np
import cv2

class FinalWorkingMapping:
    def __init__(self):
        self.image_label_masks = {}
        self.dom_segmentation = None
        
    def load_and_verify_masks(self):
        """加载并验证掩码"""
        print("📋 加载并验证掩码...")
        
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 标签统计文件不存在")
            return False
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        total_house_pixels = 0
        verified_masks = 0
        
        for image_name, stats in stats_data.items():
            mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
            
            if os.path.exists(mask_file):
                mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    # 验证掩码内容
                    house_pixels = np.sum(mask == 1)
                    
                    if house_pixels > 0:
                        self.image_label_masks[image_name] = {
                            'mask': mask,
                            'stats': stats,
                            'house_pixels': house_pixels,
                            'mask_file': mask_file
                        }
                        
                        total_house_pixels += house_pixels
                        verified_masks += 1
                        
                        print(f"   ✅ {image_name}: {house_pixels:,} 房子像素 ({stats['house_percentage']:.1f}%)")
                    else:
                        print(f"   ⚠️ {image_name}: 没有房子像素")
                else:
                    print(f"   ❌ {image_name}: 无法加载掩码")
            else:
                print(f"   ❌ {image_name}: 掩码文件不存在")
        
        print(f"\n   📊 验证结果:")
        print(f"      有效掩码: {verified_masks}/{len(stats_data)}")
        print(f"      总房子像素: {total_house_pixels:,}")
        
        return verified_masks > 0
    
    def create_working_dom_mapping(self):
        """创建确保工作的DOM映射"""
        print("🎯 创建确保工作的DOM映射...")
        
        # 使用合理的DOM尺寸
        num_images = len(self.image_label_masks)
        grid_size = max(6, int(np.ceil(np.sqrt(num_images))))
        
        # 每个图像的目标尺寸（基于测试成功的经验）
        cell_size = 600  # 每个图像600x600像素
        
        dom_width = grid_size * cell_size
        dom_height = grid_size * cell_size
        
        print(f"   📐 DOM尺寸: {dom_height} x {dom_width}")
        print(f"   📊 网格: {grid_size} x {grid_size}")
        print(f"   📏 每个单元: {cell_size} x {cell_size}")
        
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        # 按房子像素数排序，确保重要的图像优先映射
        sorted_images = sorted(self.image_label_masks.items(), 
                             key=lambda x: x[1]['house_pixels'], reverse=True)
        
        print(f"   📊 按房子像素数排序 (前5名):")
        for i, (name, data) in enumerate(sorted_images[:5]):
            print(f"      {i+1}. {name}: {data['house_pixels']:,} 像素")
        
        total_mapped_pixels = 0
        successful_mappings = 0
        
        for i, (image_name, mask_data) in enumerate(sorted_images):
            print(f"\n   🎯 映射 {i+1}/{len(sorted_images)}: {image_name}")
            
            # 计算网格位置
            grid_row = i // grid_size
            grid_col = i % grid_size
            
            # 计算DOM中的位置
            start_col = grid_col * cell_size
            end_col = start_col + cell_size
            start_row = grid_row * cell_size
            end_row = start_row + cell_size
            
            # 确保在DOM范围内
            end_col = min(end_col, dom_width)
            end_row = min(end_row, dom_height)
            
            actual_width = end_col - start_col
            actual_height = end_row - start_row
            
            print(f"      📍 网格位置: ({grid_row}, {grid_col})")
            print(f"      📐 DOM区域: ({start_col}-{end_col}, {start_row}-{end_row})")
            print(f"      📏 实际尺寸: {actual_width} x {actual_height}")
            
            if actual_width > 0 and actual_height > 0:
                # 执行映射（使用测试成功的方法）
                mapped_pixels = self.execute_working_mapping(
                    mask_data['mask'], start_row, end_row, start_col, end_col
                )
                
                total_mapped_pixels += mapped_pixels
                if mapped_pixels > 0:
                    successful_mappings += 1
                
                print(f"      ✅ 成功映射 {mapped_pixels:,} 个房子像素")
            else:
                print(f"      ❌ 无效区域尺寸")
        
        # 验证映射结果
        final_house_pixels = np.sum(self.dom_segmentation == 1)
        
        print(f"\n   📊 最终映射统计:")
        print(f"      成功映射: {successful_mappings}/{len(sorted_images)} 个图像")
        print(f"      总映射像素: {total_mapped_pixels:,}")
        print(f"      DOM中房子像素: {final_house_pixels:,}")
        
        if final_house_pixels > 0:
            print(f"   ✅ 映射验证成功！")
            return True
        else:
            print(f"   ❌ 映射验证失败！")
            return False
    
    def execute_working_mapping(self, label_mask, start_row, end_row, start_col, end_col):
        """执行确保工作的映射（基于测试成功的方法）"""
        actual_height = end_row - start_row
        actual_width = end_col - start_col
        
        if actual_height <= 0 or actual_width <= 0:
            return 0
        
        # 使用与测试相同的缩放方法
        scaled_mask = cv2.resize(label_mask, (actual_width, actual_height), 
                               interpolation=cv2.INTER_NEAREST)
        
        # 验证缩放后的内容
        unique_values = np.unique(scaled_mask)
        house_pixels_in_scaled = np.sum(scaled_mask == 1)
        
        if house_pixels_in_scaled == 0:
            # 如果缩放后没有房子像素，尝试其他插值方法
            scaled_mask = cv2.resize(label_mask, (actual_width, actual_height), 
                                   interpolation=cv2.INTER_AREA)
            scaled_mask = (scaled_mask > 127).astype(np.uint8)
            house_pixels_in_scaled = np.sum(scaled_mask == 1)
        
        if house_pixels_in_scaled > 0:
            # 获取DOM区域
            dom_region = self.dom_segmentation[start_row:end_row, start_col:end_col]
            
            # 直接应用标签
            house_pixels = scaled_mask == 1
            dom_region[house_pixels] = 1
            
            return np.sum(house_pixels)
        else:
            return 0
    
    def save_working_results(self):
        """保存工作结果"""
        print("💾 保存工作结果...")
        
        # 保存分割掩码
        cv2.imwrite('final_working_mapping_mask.png', self.dom_segmentation)
        
        # 创建彩色结果
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)
        
        # 使用高对比度颜色
        segmentation_rgb[self.dom_segmentation == 0] = [0, 0, 0]        # 黑色背景
        segmentation_rgb[self.dom_segmentation == 1] = [255, 255, 0]    # 黄色房子
        
        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('final_working_mapping_result.png', segmentation_bgr)
        
        # 创建多个尺寸版本
        scales = [0.02, 0.05, 0.1, 0.2, 0.5]
        for scale in scales:
            small_height = int(dom_height * scale)
            small_width = int(dom_width * scale)
            
            if small_height > 0 and small_width > 0:
                small_result = cv2.resize(segmentation_bgr, (small_width, small_height), 
                                        interpolation=cv2.INTER_NEAREST)
                cv2.imwrite(f'final_working_mapping_{int(scale*100)}percent.png', small_result)
                print(f"   ✅ {int(scale*100)}%版本: final_working_mapping_{int(scale*100)}percent.png ({small_width}x{small_height})")
        
        # 创建超小版本
        tiny_result = cv2.resize(segmentation_bgr, (200, 200), interpolation=cv2.INTER_NEAREST)
        cv2.imwrite('final_working_mapping_tiny.png', tiny_result)
        print(f"   ✅ 超小版本: final_working_mapping_tiny.png (200x200)")
        
        # 保存详细信息
        house_pixels = int(np.sum(self.dom_segmentation == 1))
        total_pixels = dom_height * dom_width
        
        mapping_info = {
            'dom_shape': [dom_height, dom_width],
            'num_images': len(self.image_label_masks),
            'total_house_pixels': house_pixels,
            'house_percentage': float(house_pixels / total_pixels * 100),
            'mapping_method': 'final_working_mapping',
            'success_verified': house_pixels > 0
        }
        
        with open('final_working_mapping_info.json', 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 工作结果已保存: final_working_mapping_result.png")
        print("   ✅ 工作掩码已保存: final_working_mapping_mask.png")
        print("   ✅ 详细信息已保存: final_working_mapping_info.json")
        
        return True
    
    def run_final_working_mapping(self):
        """运行最终工作映射"""
        print("=" * 80)
        print("🚀 最终工作映射系统 - 基于成功测试的确保工作版本")
        print("=" * 80)
        
        # 1. 加载并验证掩码
        if not self.load_and_verify_masks():
            print("❌ 步骤1失败：无法加载有效掩码")
            return False
        
        # 2. 创建工作映射
        if not self.create_working_dom_mapping():
            print("❌ 步骤2失败：映射过程失败")
            return False
        
        # 3. 保存结果
        if not self.save_working_results():
            print("❌ 步骤3失败：无法保存结果")
            return False
        
        print("\n" + "=" * 80)
        print("🚀 最终工作映射完成！")
        print("=" * 80)
        print("📁 生成的最终工作文件:")
        print("   • final_working_mapping_result.png - 完整结果")
        print("   • final_working_mapping_tiny.png - 超小版本 ⭐强烈推荐")
        print("   • final_working_mapping_2percent.png - 2%版本")
        print("   • final_working_mapping_5percent.png - 5%版本")
        print("   • final_working_mapping_10percent.png - 10%版本")
        print("   • final_working_mapping_20percent.png - 20%版本")
        print("   • final_working_mapping_50percent.png - 50%版本")
        print("   • final_working_mapping_mask.png - 分割掩码")
        print("   • final_working_mapping_info.json - 详细信息")
        
        print("\n🚀 最终工作系统特点:")
        print("   ✅ 基于成功测试的方法")
        print("   📊 验证掩码有效性")
        print("   🎯 确保映射成功")
        print("   📐 合理的DOM尺寸")
        print("   🔧 多种插值方法备选")
        print("   📁 多尺寸输出")
        print("   ✅ 映射成功验证")
        
        return True

def main():
    """主函数"""
    system = FinalWorkingMapping()
    success = system.run_final_working_mapping()
    
    if success:
        print("\n🎉 最终工作映射成功！")
        print("📋 成功要素:")
        print("   ✅ 验证了掩码有效性")
        print("   ✅ 使用了测试成功的方法")
        print("   ✅ 确保了映射成功")
        print("   ✅ 生成了可见结果")
        print("\n💡 强烈建议查看: final_working_mapping_tiny.png (200x200)")
    else:
        print("\n❌ 最终工作映射失败")

if __name__ == "__main__":
    main()
