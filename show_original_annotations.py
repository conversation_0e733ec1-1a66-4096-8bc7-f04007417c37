#!/usr/bin/env python3
"""
显示原始图像上的标注
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
import os

def setup_chinese_font():
    """设置中文字体"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

def show_annotations_on_image(image_name, max_annotations=4):
    """在原始图像上显示标注"""
    chinese_ok = setup_chinese_font()
    
    # 读取标注数据
    with open('label.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 找到对应图像的标注
    annotations = None
    for task in data:
        image_path = task['data']['image']
        if image_name in image_path:
            annotations = []
            if task['annotations']:
                for annotation in task['annotations']:
                    for result in annotation['result']:
                        if result['type'] == 'polygonlabels':
                            annotations.append({
                                'points': result['value']['points'],
                                'labels': result['value']['polygonlabels'],
                                'original_width': result['original_width'],
                                'original_height': result['original_height']
                            })
            break
    
    if not annotations:
        print(f"未找到图像 {image_name} 的标注数据")
        return
    
    # 读取图像
    if not os.path.exists(image_name):
        print(f"未找到图像文件: {image_name}")
        return
    
    image = cv2.imread(image_name)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    height, width = image.shape[:2]
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(15, 12))
    ax.imshow(image_rgb)
    
    # 绘制标注（限制数量以避免过于拥挤）
    colors = ['red', 'yellow', 'cyan', 'magenta', 'lime', 'orange']
    
    for i, annotation in enumerate(annotations[:max_annotations]):
        points = annotation['points']
        labels = annotation['labels']
        color = colors[i % len(colors)]
        
        # 转换百分比坐标到像素坐标
        pixel_points = []
        for point in points:
            x_percent, y_percent = point
            x_pixel = (x_percent / 100.0) * width
            y_pixel = (y_percent / 100.0) * height
            pixel_points.append([x_pixel, y_pixel])
        
        # 创建多边形
        polygon = Polygon(pixel_points, fill=False, edgecolor=color, linewidth=3, alpha=0.8)
        ax.add_patch(polygon)
        
        # 添加标签
        if pixel_points:
            center_x = np.mean([p[0] for p in pixel_points])
            center_y = np.mean([p[1] for p in pixel_points])
            
            if chinese_ok:
                label_text = f"标注 #{i+1}\n{labels[0]}"
            else:
                label_text = f"Annotation #{i+1}\n{labels[0]}"
            
            ax.text(center_x, center_y, label_text, 
                   color='white', fontsize=10, ha='center', va='center', fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.7))
    
    # 设置标题
    if chinese_ok:
        title = f"原始图像标注: {image_name}\n共 {len(annotations)} 个标注区域"
    else:
        title = f"Original Image Annotations: {image_name}\nTotal {len(annotations)} annotation regions"
    
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    ax.axis('off')
    
    # 添加图例
    if chinese_ok:
        legend_text = f"显示前 {min(max_annotations, len(annotations))} 个标注"
    else:
        legend_text = f"Showing first {min(max_annotations, len(annotations))} annotations"
    
    ax.text(0.02, 0.98, legend_text, transform=ax.transAxes, 
           fontsize=12, verticalalignment='top',
           bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图像
    output_name = f"annotated_{image_name.replace('.JPG', '.png')}"
    plt.savefig(output_name, dpi=200, bbox_inches='tight')
    
    if chinese_ok:
        print(f"标注可视化已保存: {output_name}")
    else:
        print(f"Annotation visualization saved: {output_name}")
    
    plt.close()

def main():
    """主函数 - 显示几个示例图像的标注"""
    chinese_ok = setup_chinese_font()
    
    if chinese_ok:
        print("开始创建原始图像标注可视化...")
    else:
        print("Creating original image annotation visualizations...")
    
    # 选择几个有代表性的图像进行可视化
    sample_images = [
        'DJI_20250111173221_0001_V.JPG',  # 第一张图像
        'DJI_20250111173223_0002_V.JPG',  # 第二张图像
        'DJI_20250111173226_0003_V.JPG',  # 第三张图像
    ]
    
    for image_name in sample_images:
        if os.path.exists(image_name):
            show_annotations_on_image(image_name)
        else:
            print(f"跳过不存在的图像: {image_name}")
    
    if chinese_ok:
        print("\n原始图像标注可视化完成！")
        print("生成的文件:")
    else:
        print("\nOriginal image annotation visualization completed!")
        print("Generated files:")
    
    for image_name in sample_images:
        output_name = f"annotated_{image_name.replace('.JPG', '.png')}"
        if os.path.exists(output_name):
            print(f"- {output_name}")

if __name__ == "__main__":
    main()
