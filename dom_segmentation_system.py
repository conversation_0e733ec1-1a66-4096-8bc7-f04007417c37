#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOM语义分割系统
基于DOM-原始影像对应关系，将原始影像的标签映射到DOM中，实现DOM分割
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
from PIL import Image
import urllib.parse
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加地理处理相关导入
try:
    import rasterio
    from rasterio.transform import from_bounds
    from rasterio.warp import transform_bounds, transform
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio可用，将使用高精度地理坐标转换")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("⚠️ Rasterio不可用，将使用简化坐标转换")

# 设置matplotlib中文字体
def setup_chinese_font():
    """设置中文字体"""
    import matplotlib.font_manager as fm
    
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'DejaVu Sans']
    
    for font_name in chinese_fonts:
        try:
            font_list = [f.name for f in fm.fontManager.ttflist]
            if font_name in font_list:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"   🔤 使用中文字体: {font_name}")
                return font_name
        except:
            continue
    
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("   ⚠️ 未找到中文字体，使用默认字体")
    return 'DejaVu Sans'

CHINESE_FONT = setup_chinese_font()

class DOMSegmentationSystem:
    def __init__(self):
        self.annotations = {}  # 原始影像标注数据
        self.dom_data = None
        self.dom_path = None
        self.dom_transform = None  # DOM地理变换矩阵
        self.dom_crs = None  # DOM坐标参考系统
        self.dom_bounds = None  # DOM地理边界
        self.geographic_annotations = {}  # 地理坐标标注数据
        self.pixel_mapping = {}  # DOM像元到原始影像的映射关系
        self.dom_segmentation = None  # DOM分割结果
        self.label_colors = {
            'False Positive-Confusing (1)': 1,
            'Deciduous Vegetation (2)': 2,
            'Building': 3,
            'Road': 4,
            'Water': 5,
            'Vegetation': 6,
            'Background': 0  # 背景/未标注区域
        }
        self.color_map = {
            0: (0, 0, 0),           # 背景 - 黑色
            1: (255, 0, 0),         # False Positive-Confusing - 红色
            2: (0, 255, 0),         # Deciduous Vegetation - 绿色
            3: (0, 0, 255),         # Building - 蓝色
            4: (255, 255, 0),       # Road - 黄色
            5: (0, 255, 255),       # Water - 青色
            6: (255, 0, 255)        # Vegetation - 紫色
        }
    
    def load_annotations(self):
        """加载原始影像标注数据"""
        print("🏷️ 正在加载原始影像标注数据...")
        
        try:
            with open('label.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for item in data:
                image_path = item['data']['image']
                raw_filename = os.path.basename(image_path)
                decoded_path = urllib.parse.unquote(raw_filename)
                
                if '\\' in decoded_path:
                    filename = decoded_path.split('\\')[-1]
                elif '/' in decoded_path:
                    filename = decoded_path.split('/')[-1]
                else:
                    filename = decoded_path
                
                annotations = []
                if 'annotations' in item and item['annotations']:
                    for annotation in item['annotations']:
                        if 'result' in annotation:
                            for result in annotation['result']:
                                if result['type'] == 'polygonlabels':
                                    points = result['value']['points']
                                    labels = result['value']['polygonlabels']
                                    
                                    annotations.append({
                                        'type': 'polygon',
                                        'points': points,
                                        'label': labels[0] if labels else 'Background'
                                    })
                
                if annotations:
                    self.annotations[filename] = annotations
                    print(f"   ✅ {filename}: {len(annotations)} 个标注")
            
            print(f"📋 成功加载 {len(self.annotations)} 张图像的标注数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载标注数据失败: {e}")
            return False
    
    def load_dom_data(self):
        """加载DOM数据和地理参考信息"""
        print("🗺️ 正在加载DOM数据...")

        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False

        try:
            # 使用OpenCV读取图像数据
            self.dom_data = cv2.imread(dom_file, cv2.IMREAD_UNCHANGED)
            if self.dom_data is None:
                print("❌ 无法读取DOM文件")
                return False

            self.dom_path = dom_file
            print(f"   ✅ DOM尺寸: {self.dom_data.shape}")

            # 使用rasterio读取地理参考信息
            if RASTERIO_AVAILABLE:
                try:
                    with rasterio.open(dom_file) as src:
                        self.dom_transform = src.transform
                        self.dom_crs = src.crs
                        self.dom_bounds = src.bounds
                        print(f"   ✅ DOM地理信息: CRS={self.dom_crs}")
                        print(f"   ✅ DOM边界: {self.dom_bounds}")
                        print(f"   ✅ DOM变换矩阵: {self.dom_transform}")
                except Exception as e:
                    print(f"   ⚠️ 无法读取DOM地理信息: {e}")
                    return False
            else:
                print("   ⚠️ Rasterio不可用，无法读取地理信息")
                return False

            return True

        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False

    def load_geographic_annotations(self):
        """加载地理坐标标注数据"""
        print("🌍 正在加载地理坐标标注数据...")

        geo_file = 'geographic_annotations.json'
        if not os.path.exists(geo_file):
            print("❌ 地理标注文件不存在")
            return False

        try:
            with open(geo_file, 'r', encoding='utf-8') as f:
                geo_data = json.load(f)

            self.geographic_annotations = geo_data.get('annotations', {})
            print(f"   ✅ 加载了 {len(self.geographic_annotations)} 张图像的地理标注数据")

            # 显示地理边界信息
            if 'bounds' in geo_data:
                bounds = geo_data['bounds']
                print(f"   ✅ 地理边界: 经度 {bounds[0]:.6f} - {bounds[2]:.6f}, 纬度 {bounds[1]:.6f} - {bounds[3]:.6f}")

            return True

        except Exception as e:
            print(f"❌ 加载地理标注数据失败: {e}")
            return False
    
    def load_pixel_mapping(self):
        """基于地理坐标建立DOM像元映射关系"""
        print("🔗 正在建立基于地理坐标的DOM像元映射关系...")

        if not RASTERIO_AVAILABLE:
            print("❌ 需要rasterio库进行地理坐标转换")
            return False

        if self.dom_transform is None or self.geographic_annotations is None:
            print("❌ DOM地理信息或标注数据未加载")
            return False

        try:
            dom_height, dom_width = self.dom_data.shape[:2]
            print(f"   📐 DOM尺寸: {dom_width} x {dom_height}")

            # 创建映射矩阵
            mapping_matrix = np.zeros((dom_height, dom_width), dtype=object)
            coverage_count = np.zeros((dom_height, dom_width), dtype=int)

            # 为每个DOM像元建立地理坐标映射
            print("   🗺️ 正在建立地理坐标映射...")

            # 采样处理以提高速度（每5个像素处理一次）
            sample_step = 5
            processed_pixels = 0
            total_pixels = (dom_height // sample_step) * (dom_width // sample_step)

            for row in range(0, dom_height, sample_step):
                for col in range(0, dom_width, sample_step):
                    # 将DOM像元坐标转换为地理坐标
                    dom_x, dom_y = self.dom_transform * (col, row)

                    # 如果DOM使用投影坐标系，需要转换为WGS84
                    if self.dom_crs and self.dom_crs != 'EPSG:4326':
                        try:
                            # 转换为WGS84地理坐标
                            lon, lat = transform(self.dom_crs, 'EPSG:4326', [dom_x], [dom_y])
                            geo_lon, geo_lat = lon[0], lat[0]
                        except:
                            # 如果转换失败，假设已经是地理坐标
                            geo_lon, geo_lat = dom_x, dom_y
                    else:
                        geo_lon, geo_lat = dom_x, dom_y

                    # 找到覆盖此地理位置的所有原始影像
                    covering_images = []
                    for image_name, annotations in self.geographic_annotations.items():
                        if self._point_in_image_coverage(geo_lon, geo_lat, annotations):
                            covering_images.append(image_name)

                    # 为该像元及其邻域分配覆盖影像
                    for r in range(max(0, row-2), min(dom_height, row+sample_step+2)):
                        for c in range(max(0, col-2), min(dom_width, col+sample_step+2)):
                            if mapping_matrix[r, c] is None:
                                mapping_matrix[r, c] = covering_images.copy()
                                coverage_count[r, c] = len(covering_images)

                    processed_pixels += 1
                    if processed_pixels % 1000 == 0:
                        progress = (processed_pixels / total_pixels) * 100
                        print(f"   📊 映射进度: {progress:.1f}% ({processed_pixels}/{total_pixels})")

            self.pixel_mapping = {
                'mapping_matrix': mapping_matrix,
                'coverage_count': coverage_count,
                'dom_width': dom_width,
                'dom_height': dom_height
            }

            # 统计覆盖情况
            unique_counts = np.unique(coverage_count[coverage_count > 0])
            print(f"   ✅ 像元映射关系建立完成")
            print(f"   📊 覆盖统计: 最大覆盖 {np.max(coverage_count)} 张影像")
            print(f"   📊 有效像元: {np.sum(coverage_count > 0)} / {dom_height * dom_width}")

            return True

        except Exception as e:
            print(f"❌ 建立像元映射关系失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _point_in_image_coverage(self, lon, lat, annotations):
        """判断地理点是否在影像的标注覆盖范围内"""
        if not annotations:
            return False

        # 计算所有标注的边界框
        all_lons = []
        all_lats = []

        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                for coord in coords:
                    all_lons.append(coord[0])
                    all_lats.append(coord[1])

        if not all_lons or not all_lats:
            return False

        # 扩展边界框以包含影像的完整覆盖范围
        min_lon, max_lon = min(all_lons), max(all_lons)
        min_lat, max_lat = min(all_lats), max(all_lats)

        # 扩展边界（假设影像覆盖范围比标注范围大）
        lon_range = max_lon - min_lon
        lat_range = max_lat - min_lat

        # 扩展50%的范围
        expansion_factor = 0.5
        min_lon -= lon_range * expansion_factor
        max_lon += lon_range * expansion_factor
        min_lat -= lat_range * expansion_factor
        max_lat += lat_range * expansion_factor

        # 判断点是否在扩展的边界框内
        return min_lon <= lon <= max_lon and min_lat <= lat <= max_lat

    def _get_label_at_geographic_point(self, lon, lat, image_name):
        """获取指定地理点在指定影像中的标签"""
        if image_name not in self.geographic_annotations:
            return 0

        annotations = self.geographic_annotations[image_name]

        # 检查点是否在任何标注多边形内
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]

                # 使用射线法判断点是否在多边形内
                if self._point_in_polygon(lon, lat, coords):
                    # 获取标签ID
                    if 'properties' in annotation and 'label' in annotation['properties']:
                        label_name = annotation['properties']['label']
                        return self.label_colors.get(label_name, 0)
                    else:
                        # 默认返回第一个非背景标签
                        return 1

        return 0  # 背景

    def _point_in_polygon(self, x, y, polygon_coords):
        """使用射线法判断点是否在多边形内"""
        n = len(polygon_coords)
        inside = False

        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside
    
    def create_image_segmentation_mask(self, image_filename, image_width, image_height):
        """为单张原始影像创建分割掩码"""
        if image_filename not in self.annotations:
            return np.zeros((image_height, image_width), dtype=np.uint8)
        
        mask = np.zeros((image_height, image_width), dtype=np.uint8)
        annotations = self.annotations[image_filename]
        
        for annotation in annotations:
            if annotation['type'] == 'polygon':
                # 转换百分比坐标为绝对坐标
                points = []
                for point in annotation['points']:
                    x = int((point[0] / 100.0) * image_width)
                    y = int((point[1] / 100.0) * image_height)
                    points.append([x, y])
                
                # 获取标签ID
                label = annotation['label']
                label_id = self.label_colors.get(label, 0)
                
                # 绘制多边形掩码
                if len(points) >= 3:
                    poly_array = np.array(points, dtype=np.int32)
                    cv2.fillPoly(mask, [poly_array], label_id)
        
        return mask
    
    def map_labels_to_dom(self):
        """基于地理坐标将原始影像标签映射到DOM"""
        print("🎯 正在基于地理坐标将原始影像标签映射到DOM...")

        if not self.pixel_mapping or not self.geographic_annotations:
            print("❌ 像元映射关系或地理标注数据未加载")
            return False

        if not RASTERIO_AVAILABLE:
            print("❌ 需要rasterio库进行地理坐标转换")
            return False

        dom_height, dom_width = self.dom_data.shape[:2]
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)

        print(f"   📐 DOM尺寸: {dom_width} x {dom_height}")
        print(f"   📸 可用影像: {len(self.geographic_annotations)} 张")

        # 映射到DOM
        mapping_matrix = self.pixel_mapping['mapping_matrix']
        processed_pixels = 0
        total_pixels = 0

        # 计算总像素数
        sample_step = 5
        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                total_pixels += 1
        
        print("   🗺️ 开始基于地理坐标的标签映射...")

        for row in range(0, dom_height, sample_step):
            for col in range(0, dom_width, sample_step):
                covering_images = mapping_matrix[row, col]

                if covering_images and len(covering_images) > 0:
                    # 将DOM像元坐标转换为地理坐标
                    dom_x, dom_y = self.dom_transform * (col, row)

                    # 转换为WGS84地理坐标
                    if self.dom_crs and self.dom_crs != 'EPSG:4326':
                        try:
                            lon, lat = transform(self.dom_crs, 'EPSG:4326', [dom_x], [dom_y])
                            geo_lon, geo_lat = lon[0], lat[0]
                        except:
                            geo_lon, geo_lat = dom_x, dom_y
                    else:
                        geo_lon, geo_lat = dom_x, dom_y

                    # 收集所有覆盖影像的标签投票
                    labels_votes = {}

                    for image_name in covering_images:
                        if image_name in self.geographic_annotations:
                            # 获取该影像在此地理位置的标签
                            label = self._get_label_at_geographic_point(
                                geo_lon, geo_lat, image_name
                            )
                            if label > 0:  # 非背景标签
                                labels_votes[label] = labels_votes.get(label, 0) + 1

                    # 选择得票最多的标签
                    if labels_votes:
                        final_label = max(labels_votes.keys(), key=lambda k: labels_votes[k])

                        # 填充邻域区域
                        for r in range(max(0, row-2), min(dom_height, row+sample_step+2)):
                            for c in range(max(0, col-2), min(dom_width, col+sample_step+2)):
                                if self.dom_segmentation[r, c] == 0:  # 只填充未分配的像元
                                    self.dom_segmentation[r, c] = final_label

                processed_pixels += 1
                if processed_pixels % 1000 == 0:
                    progress = (processed_pixels / total_pixels) * 100
                    print(f"   📊 映射进度: {progress:.1f}% ({processed_pixels}/{total_pixels})")
        
        # 统计分割结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        print(f"\n   🎯 DOM分割完成!")
        print(f"   📊 分割统计:")
        
        label_names = {v: k for k, v in self.label_colors.items()}
        for label, count in zip(unique_labels, counts):
            label_name = label_names.get(label, f"Unknown({label})")
            percentage = (count / (dom_height * dom_width)) * 100
            print(f"      • {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        return True

    def create_segmentation_visualization(self):
        """创建DOM分割可视化"""
        print("🎨 正在创建DOM分割可视化...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 创建彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        # 保存分割结果
        cv2.imwrite('dom_segmentation_result.png', segmentation_rgb)
        print(f"   ✅ DOM分割结果已保存: dom_segmentation_result.png")

        # 创建对比可视化
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('DOM语义分割结果可视化', fontsize=18, fontweight='bold', fontfamily=CHINESE_FONT)

        # 1. 原始DOM
        ax1 = axes[0, 0]
        if len(self.dom_data.shape) == 3:
            dom_display = cv2.cvtColor(self.dom_data, cv2.COLOR_BGR2RGB)
        else:
            dom_display = self.dom_data

        # 降采样显示
        sample_factor = max(1, max(dom_display.shape[:2]) // 1000)
        dom_sampled = dom_display[::sample_factor, ::sample_factor]

        ax1.imshow(dom_sampled)
        ax1.set_title('原始DOM影像', fontfamily=CHINESE_FONT, fontsize=14)
        ax1.axis('off')

        # 2. 分割结果
        ax2 = axes[0, 1]
        seg_sampled = segmentation_rgb[::sample_factor, ::sample_factor]
        ax2.imshow(seg_sampled)
        ax2.set_title('DOM语义分割结果', fontfamily=CHINESE_FONT, fontsize=14)
        ax2.axis('off')

        # 3. 分割统计
        ax3 = axes[1, 0]
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)

        # 准备数据
        label_names = []
        label_colors_plot = []
        label_counts = []

        label_name_map = {v: k for k, v in self.label_colors.items()}

        for label, count in zip(unique_labels, counts):
            if count > 1000:  # 只显示像元数量大于1000的类别
                name = label_name_map.get(label, f"Unknown({label})")
                if name == 'Background':
                    name = '背景'
                elif name == 'False Positive-Confusing (1)':
                    name = '误检区域'
                elif name == 'Deciduous Vegetation (2)':
                    name = '落叶植被'

                label_names.append(name)
                color = self.color_map.get(label, (128, 128, 128))
                label_colors_plot.append([c/255.0 for c in color])
                label_counts.append(count)

        bars = ax3.bar(range(len(label_names)), label_counts, color=label_colors_plot, alpha=0.8, edgecolor='black')
        ax3.set_title('DOM分割类别统计', fontfamily=CHINESE_FONT, fontsize=14)
        ax3.set_xlabel('语义类别', fontfamily=CHINESE_FONT)
        ax3.set_ylabel('像元数量', fontfamily=CHINESE_FONT)
        ax3.set_xticks(range(len(label_names)))
        ax3.set_xticklabels(label_names, rotation=45, ha='right', fontfamily=CHINESE_FONT)
        ax3.set_yscale('log')
        ax3.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, label_counts):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height * 1.1,
                    f'{count:,}', ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 4. 分割覆盖率饼图
        ax4 = axes[1, 1]

        # 计算百分比
        total_pixels = np.sum(label_counts)
        percentages = [count/total_pixels*100 for count in label_counts]

        wedges, texts, autotexts = ax4.pie(label_counts, labels=label_names, colors=label_colors_plot,
                                          autopct='%1.1f%%', startangle=90,
                                          textprops={'fontsize': 10, 'fontfamily': CHINESE_FONT})
        ax4.set_title('DOM语义分割覆盖率\n(总计: {:,} 像元)'.format(total_pixels),
                     fontfamily=CHINESE_FONT, fontsize=14)

        plt.tight_layout()
        plt.savefig('dom_segmentation_visualization.png', dpi=150, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print("   ✅ DOM分割可视化已保存: dom_segmentation_visualization.png")

    def save_segmentation_results(self):
        """保存分割结果和统计信息"""
        print("💾 正在保存DOM分割结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码（灰度图）
        cv2.imwrite('dom_segmentation_mask.png', self.dom_segmentation)

        # 保存分割结果统计
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)

        # 准备统计数据
        label_name_map = {v: k for k, v in self.label_colors.items()}
        segmentation_stats = {
            'dom_info': {
                'width': int(self.dom_segmentation.shape[1]),
                'height': int(self.dom_segmentation.shape[0]),
                'total_pixels': int(self.dom_segmentation.shape[0] * self.dom_segmentation.shape[1])
            },
            'segmentation_statistics': {},
            'label_mapping': self.label_colors,
            'color_mapping': {str(k): v for k, v in self.color_map.items()},
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        total_pixels = self.dom_segmentation.shape[0] * self.dom_segmentation.shape[1]

        for label, count in zip(unique_labels, counts):
            label_name = label_name_map.get(label, f"Unknown({label})")
            percentage = (count / total_pixels) * 100

            segmentation_stats['segmentation_statistics'][label_name] = {
                'label_id': int(label),
                'pixel_count': int(count),
                'percentage': float(percentage),
                'color_rgb': self.color_map.get(label, (128, 128, 128))
            }

        # 保存JSON报告
        with open('dom_segmentation_report.json', 'w', encoding='utf-8') as f:
            json.dump(segmentation_stats, f, indent=2, ensure_ascii=False)

        print("   ✅ 分割掩码已保存: dom_segmentation_mask.png")
        print("   ✅ 分割报告已保存: dom_segmentation_report.json")

    def create_overlay_visualization(self):
        """创建DOM与分割结果的叠加可视化"""
        print("🎭 正在创建叠加可视化...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 读取原始DOM
        if len(self.dom_data.shape) == 3:
            dom_rgb = cv2.cvtColor(self.dom_data, cv2.COLOR_BGR2RGB)
        else:
            dom_rgb = cv2.cvtColor(self.dom_data, cv2.COLOR_GRAY2RGB)

        # 创建分割结果的彩色版本
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            if label_id > 0:  # 跳过背景
                mask = self.dom_segmentation == label_id
                segmentation_rgb[mask] = color

        # 创建叠加图像
        overlay = dom_rgb.copy().astype(np.float32)
        seg_mask = np.any(segmentation_rgb > 0, axis=2)  # 非背景区域

        # 在有标注的区域进行叠加
        overlay[seg_mask] = overlay[seg_mask] * 0.6 + segmentation_rgb[seg_mask].astype(np.float32) * 0.4
        overlay = overlay.astype(np.uint8)

        # 保存叠加结果
        overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('dom_segmentation_overlay.png', overlay_bgr)

        print("   ✅ 叠加可视化已保存: dom_segmentation_overlay.png")

    def run_complete_segmentation(self):
        """运行完整的DOM分割系统"""
        print("=" * 80)
        print("🎯 DOM语义分割系统")
        print("=" * 80)

        # 1. 加载标注数据
        if not self.load_annotations():
            return

        # 2. 加载DOM数据
        if not self.load_dom_data():
            return

        # 3. 加载地理坐标标注数据
        if not self.load_geographic_annotations():
            return

        # 4. 建立像元映射关系
        if not self.load_pixel_mapping():
            return

        # 5. 执行标签映射和DOM分割
        if not self.map_labels_to_dom():
            return

        # 6. 创建可视化
        self.create_segmentation_visualization()

        # 7. 保存结果
        self.save_segmentation_results()

        # 8. 创建叠加可视化
        self.create_overlay_visualization()

        print("\n" + "=" * 80)
        print("🎯 DOM语义分割完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • dom_segmentation_result.png - 彩色分割结果")
        print("   • dom_segmentation_mask.png - 灰度分割掩码")
        print("   • dom_segmentation_visualization.png - 分割可视化分析")
        print("   • dom_segmentation_overlay.png - DOM与分割结果叠加")
        print("   • dom_segmentation_report.json - 详细分割报告")

        # 输出统计摘要
        if self.dom_segmentation is not None:
            unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
            total_pixels = self.dom_segmentation.shape[0] * self.dom_segmentation.shape[1]

            print(f"\n📊 分割结果摘要:")
            print(f"   • DOM尺寸: {self.dom_segmentation.shape[1]} × {self.dom_segmentation.shape[0]}")
            print(f"   • 总像元数: {total_pixels:,}")

            label_name_map = {v: k for k, v in self.label_colors.items()}
            for label, count in zip(unique_labels, counts):
                if count > 1000:  # 只显示主要类别
                    label_name = label_name_map.get(label, f"Unknown({label})")
                    percentage = (count / total_pixels) * 100
                    print(f"   • {label_name}: {count:,} 像元 ({percentage:.2f}%)")

        print("\n✅ DOM语义分割系统运行完成！")
        print("🎨 现在您可以查看生成的分割结果和可视化文件")

def main():
    """主函数"""
    system = DOMSegmentationSystem()
    system.run_complete_segmentation()

if __name__ == "__main__":
    main()
