#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版DOM分割系统 - 诊断映射问题
"""

import os
import json
import numpy as np
import cv2
from datetime import datetime

# 地理处理相关导入
try:
    import rasterio
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio和pyproj可用")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio或pyproj不可用")

class DebugDOMSegmentation:
    def __init__(self):
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.geographic_annotations = {}
        self.original_annotations = {}
        self.transformer = None
        
        self.label_colors = {
            'False Positive-Confusing (1)': 1,
            'Deciduous Vegetation (2)': 2,
            'Background': 0
        }
    
    def load_dom_data(self):
        """加载DOM数据"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                
                # 创建坐标转换器
                self.transformer = Transformer.from_crs(self.dom_crs, 'EPSG:4326', always_xy=True)
                
                # 测试几个关键点的坐标转换
                print("   🔍 测试坐标转换:")
                test_points = [
                    (0, 0),  # 左上角
                    (src.width//2, src.height//2),  # 中心
                    (src.width-1, src.height-1)  # 右下角
                ]
                
                for col, row in test_points:
                    utm_x, utm_y = self.dom_transform * (col, row)
                    lon, lat = self.transformer.transform(utm_x, utm_y)
                    print(f"      像素({col}, {row}) -> UTM({utm_x:.2f}, {utm_y:.2f}) -> WGS84({lon:.6f}, {lat:.6f})")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def load_annotations(self):
        """加载标注数据"""
        print("📋 正在加载标注数据...")
        
        # 1. 加载地理坐标标注
        geo_file = 'geographic_annotations.json'
        if os.path.exists(geo_file):
            try:
                with open(geo_file, 'r', encoding='utf-8') as f:
                    geo_data = json.load(f)
                self.geographic_annotations = geo_data.get('annotations', {})
                print(f"   ✅ 加载了 {len(self.geographic_annotations)} 张图像的地理标注数据")
                
                if 'bounds' in geo_data:
                    bounds = geo_data['bounds']
                    print(f"   ✅ 标注边界: 经度 {bounds[0]:.6f} - {bounds[2]:.6f}, 纬度 {bounds[1]:.6f} - {bounds[3]:.6f}")
                
                # 分析第一个图像的地理标注
                if self.geographic_annotations:
                    first_image = list(self.geographic_annotations.keys())[0]
                    first_annotations = self.geographic_annotations[first_image]
                    print(f"   🔍 第一张图像 {first_image} 有 {len(first_annotations)} 个标注")
                    
                    if first_annotations:
                        first_annotation = first_annotations[0]
                        if 'geometry' in first_annotation and 'coordinates' in first_annotation['geometry']:
                            coords = first_annotation['geometry']['coordinates'][0]
                            lons = [c[0] for c in coords]
                            lats = [c[1] for c in coords]
                            print(f"      标注范围: 经度 {min(lons):.6f} - {max(lons):.6f}, 纬度 {min(lats):.6f} - {max(lats):.6f}")
                
            except Exception as e:
                print(f"   ⚠️ 加载地理标注失败: {e}")
        
        # 2. 加载像素标注
        pixel_file = 'label.json'
        if os.path.exists(pixel_file):
            try:
                with open(pixel_file, 'r', encoding='utf-8') as f:
                    label_data = json.load(f)
                
                # 转换数据结构
                self.original_annotations = {}
                for task in label_data:
                    if 'data' in task and 'image' in task['data']:
                        image_path = task['data']['image']
                        image_name = os.path.basename(image_path.split('?d=')[-1])
                        
                        annotations = []
                        if 'annotations' in task:
                            for annotation in task['annotations']:
                                if 'result' in annotation:
                                    for result in annotation['result']:
                                        if 'value' in result and 'points' in result['value']:
                                            points = result['value']['points']
                                            width = result.get('original_width', 5280)
                                            height = result.get('original_height', 3956)
                                            
                                            pixel_points = []
                                            for point in points:
                                                x = int((point[0] / 100.0) * width)
                                                y = int((point[1] / 100.0) * height)
                                                pixel_points.extend([x, y])
                                            
                                            label = 1
                                            if 'polygonlabels' in result['value']:
                                                label_name = result['value']['polygonlabels'][0]
                                                label = self.label_colors.get(label_name, 1)
                                            
                                            annotations.append({
                                                'segmentation': [pixel_points],
                                                'category_id': label
                                            })
                        
                        if annotations:
                            self.original_annotations[image_name] = annotations
                
                print(f"   ✅ 加载了 {len(self.original_annotations)} 张图像的像素标注数据")
                
            except Exception as e:
                print(f"   ⚠️ 加载像素标注失败: {e}")
        
        return True
    
    def debug_mapping(self):
        """调试映射过程"""
        print("🔍 开始调试映射过程...")
        
        bands, dom_height, dom_width = self.dom_data.shape
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
            print(f"   🎭 有效像元: {np.sum(valid_mask):,} / {dom_height * dom_width:,}")
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        # 测试几个关键点的映射
        test_points = [
            (dom_width//4, dom_height//4),
            (dom_width//2, dom_height//2),
            (3*dom_width//4, 3*dom_height//4)
        ]
        
        print("   🔍 测试关键点的映射:")
        for col, row in test_points:
            if not valid_mask[row, col]:
                print(f"      像素({col}, {row}): 透明区域，跳过")
                continue
            
            # 转换为地理坐标
            utm_x, utm_y = self.dom_transform * (col, row)
            geo_lon, geo_lat = self.transformer.transform(utm_x, utm_y)
            
            print(f"      像素({col}, {row}) -> 地理坐标({geo_lon:.6f}, {geo_lat:.6f})")
            
            # 检查哪些影像覆盖此点
            covering_images = []
            for image_name, annotations in self.geographic_annotations.items():
                if self._point_in_image_coverage(geo_lon, geo_lat, annotations):
                    covering_images.append(image_name)
            
            print(f"         覆盖影像: {len(covering_images)} 张 - {covering_images[:3]}{'...' if len(covering_images) > 3 else ''}")
            
            # 检查标签
            labels_found = []
            for image_name in covering_images[:3]:  # 只检查前3张
                label = self._get_label_from_geographic_point(geo_lon, geo_lat, image_name)
                if label > 0:
                    labels_found.append((image_name, label))
            
            print(f"         找到标签: {labels_found}")
        
        # 统计地理边界覆盖情况
        print("\n   📊 地理边界覆盖分析:")
        total_images = len(self.geographic_annotations)
        images_with_bounds = 0
        
        for image_name, annotations in self.geographic_annotations.items():
            bounds = self._get_image_geographic_bounds(image_name)
            if bounds:
                images_with_bounds += 1
                if images_with_bounds <= 3:  # 只显示前3个
                    print(f"      {image_name}: 经度 {bounds['min_lon']:.6f} - {bounds['max_lon']:.6f}, 纬度 {bounds['min_lat']:.6f} - {bounds['max_lat']:.6f}")
        
        print(f"   📊 有地理边界的影像: {images_with_bounds} / {total_images}")
    
    def _get_image_geographic_bounds(self, image_name):
        """获取图像的地理边界"""
        if image_name not in self.geographic_annotations:
            return None
        
        annotations = self.geographic_annotations[image_name]
        all_lons, all_lats = [], []
        
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                for coord in coords:
                    all_lons.append(coord[0])
                    all_lats.append(coord[1])
        
        if not all_lons:
            return None
        
        return {
            'min_lon': min(all_lons),
            'max_lon': max(all_lons),
            'min_lat': min(all_lats),
            'max_lat': max(all_lats)
        }
    
    def _point_in_image_coverage(self, lon, lat, annotations):
        """判断点是否在影像覆盖范围内"""
        if not annotations:
            return False
        
        all_lons, all_lats = [], []
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                for coord in coords:
                    all_lons.append(coord[0])
                    all_lats.append(coord[1])
        
        if not all_lons:
            return False
        
        min_lon, max_lon = min(all_lons), max(all_lons)
        min_lat, max_lat = min(all_lats), max(all_lats)
        
        # 使用较小的扩展因子进行测试
        expansion_factor = 0.1
        lon_range = max_lon - min_lon
        lat_range = max_lat - min_lat
        
        min_lon -= lon_range * expansion_factor
        max_lon += lon_range * expansion_factor
        min_lat -= lat_range * expansion_factor
        max_lat += lat_range * expansion_factor
        
        return min_lon <= lon <= max_lon and min_lat <= lat <= max_lat
    
    def _get_label_from_geographic_point(self, lon, lat, image_name):
        """从地理坐标获取标签"""
        if image_name not in self.geographic_annotations:
            return 0
        
        annotations = self.geographic_annotations[image_name]
        
        for annotation in annotations:
            if 'geometry' in annotation and 'coordinates' in annotation['geometry']:
                coords = annotation['geometry']['coordinates'][0]
                
                if self._point_in_polygon(lon, lat, coords):
                    return 1  # 简化：直接返回标签1
        
        return 0
    
    def _point_in_polygon(self, x, y, polygon_coords):
        """判断点是否在多边形内"""
        n = len(polygon_coords)
        inside = False
        
        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def run_debug(self):
        """运行调试"""
        print("=" * 80)
        print("🔍 DOM分割系统调试")
        print("=" * 80)
        
        if not self.load_dom_data():
            return False
        
        if not self.load_annotations():
            return False
        
        self.debug_mapping()
        
        print("\n" + "=" * 80)
        print("🔍 调试完成")
        print("=" * 80)
        
        return True

def main():
    """主函数"""
    system = DebugDOMSegmentation()
    system.run_debug()

if __name__ == "__main__":
    main()
