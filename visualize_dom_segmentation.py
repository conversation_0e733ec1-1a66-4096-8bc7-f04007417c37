#!/usr/bin/env python3
"""
DOM分割结果可视化
"""

import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import os

def setup_chinese_font():
    """设置中文字体"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

def visualize_dom_segmentation():
    """可视化DOM分割结果"""
    chinese_ok = setup_chinese_font()
    
    # 检查文件
    if not os.path.exists('dom_segmentation.png'):
        print("DOM分割结果文件不存在，请先运行 annotation_reprojection.py")
        return
    
    if not os.path.exists('dom_segmentation_stats.json'):
        print("统计文件不存在")
        return
    
    # 读取数据
    dom_labels = cv2.imread('dom_segmentation.png', cv2.IMREAD_GRAYSCALE)
    
    with open('dom_segmentation_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('DJI图像标注反投影到DOM结果' if chinese_ok else 'DJI Image Annotation Reprojection to DOM Results', 
                fontsize=16, fontweight='bold')
    
    # 1. DOM分割结果
    ax1 = axes[0, 0]
    im1 = ax1.imshow(dom_labels, cmap='RdYlBu_r', interpolation='nearest')
    ax1.set_title('DOM分割结果' if chinese_ok else 'DOM Segmentation Result', 
                 fontsize=12, fontweight='bold')
    ax1.set_xlabel('X (像素)' if chinese_ok else 'X (pixels)')
    ax1.set_ylabel('Y (像素)' if chinese_ok else 'Y (pixels)')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 添加标注区域的边界框
    if np.any(dom_labels > 0):
        # 找到标注区域
        coords = np.where(dom_labels > 0)
        if len(coords[0]) > 0:
            min_y, max_y = coords[0].min(), coords[0].max()
            min_x, max_x = coords[1].min(), coords[1].max()
            
            # 绘制边界框
            rect = Rectangle((min_x, min_y), max_x - min_x, max_y - min_y,
                           linewidth=2, edgecolor='red', facecolor='none')
            ax1.add_patch(rect)
    
    # 2. 标注密度热图
    ax2 = axes[0, 1]
    # 创建密度图（使用高斯模糊）
    if np.any(dom_labels > 0):
        density = cv2.GaussianBlur(dom_labels.astype(np.float32), (15, 15), 0)
        im2 = ax2.imshow(density, cmap='hot', interpolation='bilinear')
        ax2.set_title('标注密度热图' if chinese_ok else 'Annotation Density Heatmap', 
                     fontsize=12, fontweight='bold')
        plt.colorbar(im2, ax=ax2, shrink=0.8)
    else:
        ax2.text(0.5, 0.5, '无标注数据' if chinese_ok else 'No Annotation Data', 
                ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('标注密度热图' if chinese_ok else 'Annotation Density Heatmap', 
                     fontsize=12, fontweight='bold')
    
    ax2.set_xlabel('X (像素)' if chinese_ok else 'X (pixels)')
    ax2.set_ylabel('Y (像素)' if chinese_ok else 'Y (pixels)')
    
    # 3. 统计图表
    ax3 = axes[1, 0]
    
    if chinese_ok:
        labels = ['总像素', '标注像素', '背景像素']
    else:
        labels = ['Total Pixels', 'Annotated Pixels', 'Background Pixels']
    
    values = [
        stats['total_pixels'],
        stats['annotated_pixels'],
        stats['total_pixels'] - stats['annotated_pixels']
    ]
    
    colors = ['lightblue', 'red', 'lightgray']
    bars = ax3.bar(labels, values, color=colors, alpha=0.7)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
               f'{value:,}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    ax3.set_title('像素统计' if chinese_ok else 'Pixel Statistics', 
                 fontsize=12, fontweight='bold')
    ax3.set_ylabel('像素数量' if chinese_ok else 'Pixel Count')
    plt.setp(ax3.get_xticklabels(), rotation=45, ha='right')
    
    # 4. 处理信息表格
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    if chinese_ok:
        table_data = [
            ['项目', '数值'],
            ['DOM尺寸', f"{stats['dom_size'][0]} × {stats['dom_size'][1]} 像素"],
            ['地理分辨率', f"{stats['dom_resolution']:.8f} 度/像素"],
            ['处理图像数', f"{stats['processed_images']} 张"],
            ['有标注图像', f"{stats['annotated_images']} 张"],
            ['标注覆盖率', f"{stats['annotation_coverage']:.2f}%"],
            ['经度范围', f"{stats['dom_bounds'][0]:.6f}° - {stats['dom_bounds'][2]:.6f}°"],
            ['纬度范围', f"{stats['dom_bounds'][1]:.6f}° - {stats['dom_bounds'][3]:.6f}°"]
        ]
        title = "处理信息摘要"
    else:
        table_data = [
            ['Item', 'Value'],
            ['DOM Size', f"{stats['dom_size'][0]} × {stats['dom_size'][1]} pixels"],
            ['Resolution', f"{stats['dom_resolution']:.8f} deg/pixel"],
            ['Processed Images', f"{stats['processed_images']} images"],
            ['Annotated Images', f"{stats['annotated_images']} images"],
            ['Coverage', f"{stats['annotation_coverage']:.2f}%"],
            ['Longitude Range', f"{stats['dom_bounds'][0]:.6f}° - {stats['dom_bounds'][2]:.6f}°"],
            ['Latitude Range', f"{stats['dom_bounds'][1]:.6f}° - {stats['dom_bounds'][3]:.6f}°"]
        ]
        title = "Processing Information Summary"
    
    table = ax4.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='left', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 1.5)
    
    # 设置表格样式
    for i in range(len(table_data)):
        for j in range(2):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax4.set_title(title, fontsize=12, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('dom_segmentation_visualization.png', dpi=300, bbox_inches='tight')
    print("可视化结果已保存: dom_segmentation_visualization.png")
    plt.close()

def create_overlay_visualization():
    """创建叠加可视化（如果有原始DOM图像）"""
    chinese_ok = setup_chinese_font()
    
    # 检查是否有DOM分割结果
    if not os.path.exists('dom_segmentation.png'):
        return
    
    dom_labels = cv2.imread('dom_segmentation.png', cv2.IMREAD_GRAYSCALE)
    
    # 创建彩色叠加图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 创建背景（灰色）
    background = np.ones((*dom_labels.shape, 3), dtype=np.uint8) * 128
    
    # 叠加标注（红色）
    mask = dom_labels > 0
    background[mask] = [255, 0, 0]  # 红色标注
    
    ax.imshow(background)
    ax.set_title('DOM标注叠加图' if chinese_ok else 'DOM Annotation Overlay', 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('X (像素)' if chinese_ok else 'X (pixels)')
    ax.set_ylabel('Y (像素)' if chinese_ok else 'Y (pixels)')
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', label='建筑物标注' if chinese_ok else 'Building Annotation'),
        Patch(facecolor='gray', label='背景区域' if chinese_ok else 'Background Area')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('dom_annotation_overlay.png', dpi=300, bbox_inches='tight')
    print("叠加可视化已保存: dom_annotation_overlay.png")
    plt.close()

def main():
    """主函数"""
    print("开始创建DOM分割可视化...")
    
    visualize_dom_segmentation()
    create_overlay_visualization()
    
    print("可视化完成！")
    print("生成的文件:")
    print("- dom_segmentation_visualization.png: 综合可视化")
    print("- dom_annotation_overlay.png: 标注叠加图")

if __name__ == "__main__":
    main()
