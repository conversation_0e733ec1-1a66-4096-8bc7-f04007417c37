#!/usr/bin/env python3
"""
高级栅格生成器 - 支持多标签冲突解决
实现基于投票机制的DOM分类
"""

import json
import numpy as np
import cv2
from typing import Dict, List, Tuple, Set
from collections import defaultdict, Counter
import os

class AdvancedRasterGenerator:
    """高级栅格生成器，支持多标签冲突解决"""
    
    def __init__(self, geographic_annotations_file: str):
        self.geographic_annotations_file = geographic_annotations_file
        self.data = None
        self.bounds = None
        self.annotations = None
        
    def load_data(self):
        """加载地理标注数据"""
        with open(self.geographic_annotations_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        self.bounds = self.data['bounds']
        self.annotations = self.data['annotations']
        
    def create_pixel_voting_map(self, resolution: float = 0.0001) -> Tuple[np.ndarray, Dict]:
        """创建像素级投票地图，解决多标签冲突"""
        min_lon, min_lat, max_lon, max_lat = self.bounds
        
        # 计算栅格尺寸
        width = int((max_lon - min_lon) / resolution) + 1
        height = int((max_lat - min_lat) / resolution) + 1
        
        print(f"创建投票地图: {width} x {height} 像素")
        print(f"分辨率: {resolution} 度/像素")
        
        # 为每个像素创建投票记录
        # pixel_votes[y][x] = {'background': count, 'annotated': count, 'images': set()}
        pixel_votes = defaultdict(lambda: defaultdict(lambda: {
            'background': 0, 
            'annotated': 0, 
            'images': set(),
            'total_votes': 0
        }))
        
        # 地理变换参数
        transform_info = {
            'min_lon': min_lon,
            'max_lat': max_lat,
            'resolution': resolution,
            'width': width,
            'height': height
        }
        
        print("开始像素级投票统计...")
        
        # 遍历每张图像
        for image_name, image_annotations in self.annotations.items():
            print(f"处理图像: {image_name}")
            
            # 创建当前图像的标注掩码
            image_mask = np.zeros((height, width), dtype=np.uint8)
            
            # 将当前图像的所有标注绘制到掩码上
            for annotation in image_annotations:
                coords = annotation['geometry']['coordinates'][0]
                
                # 转换地理坐标到栅格坐标
                raster_points = []
                for lon, lat in coords:
                    x = int((lon - min_lon) / resolution)
                    y = int((max_lat - lat) / resolution)
                    
                    # 确保坐标在栅格范围内
                    x = max(0, min(width - 1, x))
                    y = max(0, min(height - 1, y))
                    
                    raster_points.append([x, y])
                
                # 使用OpenCV填充多边形
                if len(raster_points) >= 3:
                    points = np.array(raster_points, dtype=np.int32)
                    cv2.fillPoly(image_mask, [points], 1)
            
            # 统计当前图像对每个像素的投票
            # 关键修复：只对实际覆盖的区域进行投票，而不是整个栅格
            # 首先确定当前图像的实际覆盖范围
            coverage_mask = np.zeros((height, width), dtype=np.uint8)

            # 为每个标注区域创建覆盖掩码（扩展一些边界以包含周围区域）
            for annotation in image_annotations:
                coords = annotation['geometry']['coordinates'][0]

                # 转换地理坐标到栅格坐标
                raster_points = []
                for lon, lat in coords:
                    x = int((lon - min_lon) / resolution)
                    y = int((max_lat - lat) / resolution)

                    x = max(0, min(width - 1, x))
                    y = max(0, min(height - 1, y))

                    raster_points.append([x, y])

                if len(raster_points) >= 3:
                    points = np.array(raster_points, dtype=np.int32)
                    # 创建覆盖区域（比标注区域稍大）
                    cv2.fillPoly(coverage_mask, [points], 1)

                    # 扩展覆盖区域以包含周围像素
                    kernel = np.ones((5, 5), np.uint8)
                    coverage_mask = cv2.dilate(coverage_mask, kernel, iterations=1)

            # 只对覆盖区域内的像素进行投票
            for y in range(height):
                for x in range(width):
                    if coverage_mask[y, x] > 0:  # 只对实际覆盖的区域投票
                        pixel_votes[y][x]['images'].add(image_name)
                        pixel_votes[y][x]['total_votes'] += 1

                        if image_mask[y, x] > 0:
                            pixel_votes[y][x]['annotated'] += 1
                        else:
                            pixel_votes[y][x]['background'] += 1
        
        return pixel_votes, transform_info
    
    def resolve_conflicts_and_create_raster(self, pixel_votes: Dict, transform_info: Dict, 
                                          voting_threshold: float = 0.5) -> Tuple[np.ndarray, Dict]:
        """基于投票结果解决冲突并创建最终栅格"""
        width = transform_info['width']
        height = transform_info['height']
        
        print(f"解决多标签冲突，投票阈值: {voting_threshold}")
        
        # 创建最终栅格
        final_raster = np.zeros((height, width), dtype=np.uint8)
        
        # 统计信息
        conflict_stats = {
            'total_pixels': 0,
            'conflict_pixels': 0,
            'single_vote_pixels': 0,
            'no_vote_pixels': 0,
            'annotation_wins': 0,
            'background_wins': 0
        }
        
        # 遍历每个像素，基于投票结果决定最终标签
        for y in range(height):
            for x in range(width):
                votes = pixel_votes[y][x]
                total_votes = votes['total_votes']
                annotated_votes = votes['annotated']
                background_votes = votes['background']
                
                conflict_stats['total_pixels'] += 1
                
                if total_votes == 0:
                    # 没有任何图像覆盖此像素，标记为背景
                    final_raster[y, x] = 0
                    conflict_stats['no_vote_pixels'] += 1
                    
                elif total_votes == 1:
                    # 只有一张图像覆盖此像素
                    final_raster[y, x] = 1 if annotated_votes > 0 else 0
                    conflict_stats['single_vote_pixels'] += 1
                    
                else:
                    # 多张图像覆盖此像素，需要解决冲突
                    conflict_stats['conflict_pixels'] += 1
                    
                    # 计算标注投票比例
                    annotation_ratio = annotated_votes / total_votes
                    
                    if annotation_ratio >= voting_threshold:
                        final_raster[y, x] = 1  # 标注区域
                        conflict_stats['annotation_wins'] += 1
                    else:
                        final_raster[y, x] = 0  # 背景区域
                        conflict_stats['background_wins'] += 1
        
        return final_raster, conflict_stats
    
    def analyze_coverage_overlap(self, pixel_votes: Dict, transform_info: Dict) -> Dict:
        """分析图像覆盖和重叠情况"""
        width = transform_info['width']
        height = transform_info['height']
        
        coverage_stats = {
            'max_overlap': 0,
            'overlap_distribution': Counter(),
            'image_coverage': {},
            'total_covered_pixels': 0
        }
        
        # 统计每张图像的覆盖像素数
        image_coverage = defaultdict(int)
        
        for y in range(height):
            for x in range(width):
                votes = pixel_votes[y][x]
                total_votes = votes['total_votes']
                
                if total_votes > 0:
                    coverage_stats['total_covered_pixels'] += 1
                    coverage_stats['max_overlap'] = max(coverage_stats['max_overlap'], total_votes)
                    coverage_stats['overlap_distribution'][total_votes] += 1
                    
                    # 统计每张图像的覆盖
                    for image_name in votes['images']:
                        image_coverage[image_name] += 1
        
        coverage_stats['image_coverage'] = dict(image_coverage)
        
        return coverage_stats
    
    def save_detailed_results(self, final_raster: np.ndarray, conflict_stats: Dict, 
                            coverage_stats: Dict, transform_info: Dict, 
                            output_prefix: str = 'advanced_labels'):
        """保存详细的处理结果"""
        
        # 1. 保存最终栅格
        raster_file = f'{output_prefix}.png'
        cv2.imwrite(raster_file, final_raster * 255)
        print(f"最终栅格已保存: {raster_file}")
        
        # 2. 保存地理信息
        geo_file = f'{output_prefix}_geo_info.json'
        with open(geo_file, 'w', encoding='utf-8') as f:
            json.dump(transform_info, f, indent=2)
        print(f"地理信息已保存: {geo_file}")
        
        # 3. 保存冲突解决统计
        conflict_file = f'{output_prefix}_conflict_stats.json'
        with open(conflict_file, 'w', encoding='utf-8') as f:
            json.dump(conflict_stats, f, indent=2, ensure_ascii=False)
        print(f"冲突统计已保存: {conflict_file}")
        
        # 4. 保存覆盖分析
        coverage_file = f'{output_prefix}_coverage_stats.json'
        # 转换Counter对象为普通字典以便JSON序列化
        coverage_stats_serializable = coverage_stats.copy()
        coverage_stats_serializable['overlap_distribution'] = dict(coverage_stats['overlap_distribution'])
        
        with open(coverage_file, 'w', encoding='utf-8') as f:
            json.dump(coverage_stats_serializable, f, indent=2, ensure_ascii=False)
        print(f"覆盖分析已保存: {coverage_file}")
        
        # 5. 生成最终统计
        unique, counts = np.unique(final_raster, return_counts=True)
        total_pixels = final_raster.size
        
        final_stats = {
            'total_pixels': total_pixels,
            'background_pixels': int(counts[0]) if 0 in unique else 0,
            'annotated_pixels': int(counts[1]) if 1 in unique else 0,
            'background_percentage': (counts[0] / total_pixels * 100) if 0 in unique else 0,
            'annotated_percentage': (counts[1] / total_pixels * 100) if 1 in unique else 0
        }
        
        stats_file = f'{output_prefix}_final_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, indent=2, ensure_ascii=False)
        print(f"最终统计已保存: {stats_file}")
        
        return final_stats
    
    def run_advanced_processing(self, resolution: float = 0.0001, 
                              voting_threshold: float = 0.5,
                              output_prefix: str = 'advanced_labels'):
        """运行高级多标签冲突解决处理"""
        print("开始高级栅格化处理（多标签冲突解决）...")
        
        # 1. 加载数据
        print("1. 加载地理标注数据...")
        self.load_data()
        
        # 2. 创建像素投票地图
        print("2. 创建像素级投票地图...")
        pixel_votes, transform_info = self.create_pixel_voting_map(resolution)
        
        # 3. 分析覆盖情况
        print("3. 分析图像覆盖和重叠情况...")
        coverage_stats = self.analyze_coverage_overlap(pixel_votes, transform_info)
        
        # 4. 解决冲突并创建最终栅格
        print("4. 基于投票解决多标签冲突...")
        final_raster, conflict_stats = self.resolve_conflicts_and_create_raster(
            pixel_votes, transform_info, voting_threshold)
        
        # 5. 保存结果
        print("5. 保存处理结果...")
        final_stats = self.save_detailed_results(
            final_raster, conflict_stats, coverage_stats, transform_info, output_prefix)
        
        # 6. 打印摘要
        self.print_processing_summary(conflict_stats, coverage_stats, final_stats)
        
        return final_raster, conflict_stats, coverage_stats, final_stats
    
    def print_processing_summary(self, conflict_stats: Dict, coverage_stats: Dict, final_stats: Dict):
        """打印处理摘要"""
        print("\n" + "="*60)
        print(" 高级栅格化处理摘要")
        print("="*60)
        
        print(f"\n📊 像素统计:")
        print(f"  总像素数: {conflict_stats['total_pixels']:,}")
        print(f"  无覆盖像素: {conflict_stats['no_vote_pixels']:,}")
        print(f"  单图像覆盖: {conflict_stats['single_vote_pixels']:,}")
        print(f"  多图像冲突: {conflict_stats['conflict_pixels']:,}")
        
        print(f"\n🗳️ 冲突解决:")
        print(f"  标注胜出: {conflict_stats['annotation_wins']:,}")
        print(f"  背景胜出: {conflict_stats['background_wins']:,}")
        
        print(f"\n🗺️ 图像覆盖:")
        print(f"  最大重叠度: {coverage_stats['max_overlap']} 张图像")
        print(f"  总覆盖像素: {coverage_stats['total_covered_pixels']:,}")
        
        print(f"\n📈 最终分类结果:")
        print(f"  背景区域: {final_stats['background_pixels']:,} 像素 ({final_stats['background_percentage']:.2f}%)")
        print(f"  标注区域: {final_stats['annotated_pixels']:,} 像素 ({final_stats['annotated_percentage']:.2f}%)")
        
        print(f"\n🔍 重叠分布:")
        for overlap_count, pixel_count in sorted(coverage_stats['overlap_distribution'].items()):
            print(f"  {overlap_count}张图像重叠: {pixel_count:,} 像素")

if __name__ == "__main__":
    generator = AdvancedRasterGenerator('geographic_annotations.json')
    generator.run_advanced_processing(
        resolution=0.0001,
        voting_threshold=0.5,  # 50%以上投票才标记为标注区域
        output_prefix='dom_classification'
    )
