#!/usr/bin/env python3
"""
正射影像生成和标注映射系统
实现从原始影像到正射影像的完整流程
"""

import json
import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import os
from collections import defaultdict, Counter
import math

class OrthophotoGenerator:
    """正射影像生成器"""
    
    def __init__(self, image_dir: str, label_file: str):
        self.image_dir = image_dir
        self.label_file = label_file
        self.images_metadata = {}
        self.annotations_data = {}
        self.orthophoto_bounds = None
        self.orthophoto_resolution = None
        
    def load_image_metadata(self):
        """加载所有图像的元数据"""
        from geo_processing import DJIImageProcessor
        
        processor = DJIImageProcessor(self.image_dir, self.label_file)
        processor.load_all_images_metadata()
        processor.load_annotations()
        
        self.images_metadata = processor.images_data
        self.annotations_data = processor.annotations_data
        
        print(f"加载了 {len(self.images_metadata)} 张图像的元数据")
        print(f"加载了 {len(self.annotations_data)} 张图像的标注数据")
    
    def calculate_orthophoto_bounds(self, margin_factor: float = 0.1):
        """计算正射影像的地理边界"""
        all_lats = []
        all_lons = []
        
        for image_name, metadata in self.images_metadata.items():
            if metadata['gps_lat'] and metadata['gps_lon']:
                # 计算图像四角的地理坐标
                corners = self.calculate_image_corners(metadata)
                for lat, lon in corners:
                    all_lats.append(lat)
                    all_lons.append(lon)
        
        if not all_lats:
            raise ValueError("没有找到有效的GPS坐标")
        
        min_lat, max_lat = min(all_lats), max(all_lats)
        min_lon, max_lon = min(all_lons), max(all_lons)
        
        # 添加边距
        lat_margin = (max_lat - min_lat) * margin_factor
        lon_margin = (max_lon - min_lon) * margin_factor
        
        self.orthophoto_bounds = (
            min_lon - lon_margin,
            min_lat - lat_margin, 
            max_lon + lon_margin,
            max_lat + lat_margin
        )
        
        print(f"正射影像边界: {self.orthophoto_bounds}")
        return self.orthophoto_bounds
    
    def calculate_image_corners(self, metadata: Dict) -> List[Tuple[float, float]]:
        """计算图像四角的地理坐标"""
        width = metadata['width']
        height = metadata['height']
        
        # 图像四角的像素坐标
        corners_pixel = [
            (0, 0),           # 左上
            (width, 0),       # 右上  
            (width, height),  # 右下
            (0, height)       # 左下
        ]
        
        corners_geo = []
        for px, py in corners_pixel:
            lat, lon = self.pixel_to_geographic(px, py, metadata)
            corners_geo.append((lat, lon))
        
        return corners_geo
    
    def pixel_to_geographic(self, pixel_x: int, pixel_y: int, metadata: Dict) -> Tuple[float, float]:
        """像素坐标到地理坐标转换（简化版）"""
        width = metadata['width']
        height = metadata['height']
        focal_length = metadata['focal_length']
        altitude = metadata['gps_alt']
        center_lat = metadata['gps_lat']
        center_lon = metadata['gps_lon']
        
        # DJI M3E传感器尺寸估算
        sensor_width = 17.3  # mm
        sensor_height = 13.0  # mm
        
        # 计算地面分辨率
        gsd_x = (sensor_width * altitude) / (focal_length * width)
        gsd_y = (sensor_height * altitude) / (focal_length * height)
        
        # 计算相对于图像中心的偏移
        center_x = width / 2
        center_y = height / 2
        
        offset_x = (pixel_x - center_x) * gsd_x
        offset_y = (center_y - pixel_y) * gsd_y
        
        # 地理坐标转换
        lat_offset = offset_y / 111320.0
        lon_offset = offset_x / (111320.0 * math.cos(math.radians(center_lat)))
        
        geo_lat = center_lat + lat_offset
        geo_lon = center_lon + lon_offset
        
        return geo_lat, geo_lon
    
    def geographic_to_orthophoto(self, lat: float, lon: float) -> Tuple[int, int]:
        """地理坐标到正射影像像素坐标转换"""
        min_lon, min_lat, max_lon, max_lat = self.orthophoto_bounds
        
        # 计算正射影像中的像素位置
        ortho_x = int((lon - min_lon) / self.orthophoto_resolution)
        ortho_y = int((max_lat - lat) / self.orthophoto_resolution)
        
        return ortho_x, ortho_y
    
    def generate_orthophoto_mosaic(self, resolution: float = 0.0001) -> np.ndarray:
        """生成正射影像镶嵌"""
        self.orthophoto_resolution = resolution
        
        if not self.orthophoto_bounds:
            self.calculate_orthophoto_bounds()
        
        min_lon, min_lat, max_lon, max_lat = self.orthophoto_bounds
        
        # 计算正射影像尺寸
        ortho_width = int((max_lon - min_lon) / resolution) + 1
        ortho_height = int((max_lat - min_lat) / resolution) + 1
        
        print(f"正射影像尺寸: {ortho_width} x {ortho_height}")
        
        # 创建正射影像画布
        orthophoto = np.zeros((ortho_height, ortho_width, 3), dtype=np.uint8)
        weight_map = np.zeros((ortho_height, ortho_width), dtype=np.float32)
        
        # 处理每张原始影像
        for image_name, metadata in self.images_metadata.items():
            if not all([metadata['gps_lat'], metadata['gps_lon'], metadata['gps_alt']]):
                continue
                
            print(f"处理图像: {image_name}")
            
            # 读取原始图像
            image_path = os.path.join(self.image_dir, image_name)
            if not os.path.exists(image_path):
                continue
                
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            # 将原始图像投影到正射影像空间
            self.project_image_to_orthophoto(
                image, metadata, orthophoto, weight_map
            )
        
        # 归一化处理
        mask = weight_map > 0
        for c in range(3):
            orthophoto[:, :, c][mask] = (
                orthophoto[:, :, c][mask] / weight_map[mask]
            ).astype(np.uint8)
        
        return orthophoto
    
    def project_image_to_orthophoto(self, image: np.ndarray, metadata: Dict, 
                                   orthophoto: np.ndarray, weight_map: np.ndarray):
        """将单张图像投影到正射影像空间"""
        height, width = image.shape[:2]
        ortho_height, ortho_width = orthophoto.shape[:2]
        
        # 计算图像中心到边缘的距离权重
        center_x, center_y = width // 2, height // 2
        
        # 采样投影（每隔N个像素采样一次以提高速度）
        sample_step = max(1, min(width, height) // 100)
        
        for y in range(0, height, sample_step):
            for x in range(0, width, sample_step):
                # 转换到地理坐标
                lat, lon = self.pixel_to_geographic(x, y, metadata)
                
                # 转换到正射影像坐标
                ortho_x, ortho_y = self.geographic_to_orthophoto(lat, lon)
                
                # 检查边界
                if (0 <= ortho_x < ortho_width and 0 <= ortho_y < ortho_height):
                    # 计算权重（距离图像中心越近权重越大）
                    dist_from_center = math.sqrt(
                        (x - center_x)**2 + (y - center_y)**2
                    )
                    max_dist = math.sqrt(center_x**2 + center_y**2)
                    weight = 1.0 - (dist_from_center / max_dist)
                    weight = max(0.1, weight)  # 最小权重0.1
                    
                    # 累加像素值和权重
                    pixel_value = image[y, x]
                    orthophoto[ortho_y, ortho_x] += (pixel_value * weight).astype(np.uint8)
                    weight_map[ortho_y, ortho_x] += weight
    
    def generate_label_orthophoto(self, resolution: float = 0.0001, 
                                 voting_threshold: float = 0.5) -> np.ndarray:
        """生成标签正射影像"""
        self.orthophoto_resolution = resolution
        
        if not self.orthophoto_bounds:
            self.calculate_orthophoto_bounds()
        
        min_lon, min_lat, max_lon, max_lat = self.orthophoto_bounds
        
        # 计算正射影像尺寸
        ortho_width = int((max_lon - min_lon) / resolution) + 1
        ortho_height = int((max_lat - min_lat) / resolution) + 1
        
        print(f"标签正射影像尺寸: {ortho_width} x {ortho_height}")
        
        # 创建像素投票记录
        pixel_votes = defaultdict(lambda: {
            'background': 0,
            'annotated': 0, 
            'total_votes': 0,
            'images': set()
        })
        
        # 处理每张有标注的图像
        for image_name, annotations in self.annotations_data.items():
            if image_name not in self.images_metadata:
                continue
                
            metadata = self.images_metadata[image_name]
            if not all([metadata['gps_lat'], metadata['gps_lon'], metadata['gps_alt']]):
                continue
            
            print(f"处理标注图像: {image_name}")
            
            # 创建当前图像的标注掩码
            image_mask = self.create_annotation_mask(annotations, metadata)
            
            # 投影标注到正射影像空间
            self.project_annotations_to_orthophoto(
                image_mask, metadata, pixel_votes, image_name
            )
        
        # 基于投票解决冲突
        label_orthophoto = self.resolve_voting_conflicts(
            pixel_votes, ortho_width, ortho_height, voting_threshold
        )
        
        return label_orthophoto, pixel_votes
    
    def create_annotation_mask(self, annotations: List, metadata: Dict) -> np.ndarray:
        """为单张图像创建标注掩码"""
        width = metadata['width']
        height = metadata['height']
        mask = np.zeros((height, width), dtype=np.uint8)
        
        for annotation in annotations:
            points = annotation['points']
            
            # 转换百分比坐标到像素坐标
            pixel_points = []
            for point in points:
                x_percent, y_percent = point
                x_pixel = int((x_percent / 100.0) * width)
                y_pixel = int((y_percent / 100.0) * height)
                pixel_points.append([x_pixel, y_pixel])
            
            # 填充多边形
            if len(pixel_points) >= 3:
                points_array = np.array(pixel_points, dtype=np.int32)
                cv2.fillPoly(mask, [points_array], 1)
        
        return mask
    
    def project_annotations_to_orthophoto(self, image_mask: np.ndarray, 
                                        metadata: Dict, pixel_votes: Dict, 
                                        image_name: str):
        """将图像标注投影到正射影像空间"""
        height, width = image_mask.shape
        
        # 找到有标注的区域边界
        coords = np.where(image_mask > 0)
        if len(coords[0]) == 0:
            return
        
        min_y, max_y = coords[0].min(), coords[0].max()
        min_x, max_x = coords[1].min(), coords[1].max()
        
        # 扩展边界以包含周围区域
        margin = 50
        min_y = max(0, min_y - margin)
        max_y = min(height, max_y + margin)
        min_x = max(0, min_x - margin)
        max_x = min(width, max_x + margin)
        
        # 在扩展区域内进行投票
        sample_step = 5  # 采样步长
        
        for y in range(min_y, max_y, sample_step):
            for x in range(min_x, max_x, sample_step):
                # 转换到地理坐标
                lat, lon = self.pixel_to_geographic(x, y, metadata)
                
                # 转换到正射影像坐标
                ortho_x, ortho_y = self.geographic_to_orthophoto(lat, lon)
                
                # 记录投票
                key = (ortho_y, ortho_x)
                pixel_votes[key]['images'].add(image_name)
                pixel_votes[key]['total_votes'] += 1
                
                if image_mask[y, x] > 0:
                    pixel_votes[key]['annotated'] += 1
                else:
                    pixel_votes[key]['background'] += 1
    
    def resolve_voting_conflicts(self, pixel_votes: Dict, width: int, height: int,
                                voting_threshold: float) -> np.ndarray:
        """解决投票冲突生成最终标签正射影像"""
        label_orthophoto = np.zeros((height, width), dtype=np.uint8)
        
        for (y, x), votes in pixel_votes.items():
            if 0 <= x < width and 0 <= y < height:
                total_votes = votes['total_votes']
                annotated_votes = votes['annotated']
                
                if total_votes > 0:
                    annotation_ratio = annotated_votes / total_votes
                    if annotation_ratio >= voting_threshold:
                        label_orthophoto[y, x] = 1
        
        return label_orthophoto
    
    def save_results(self, orthophoto: np.ndarray, label_orthophoto: np.ndarray,
                    pixel_votes: Dict, output_prefix: str = 'orthophoto'):
        """保存所有结果"""
        
        # 1. 保存正射影像
        ortho_file = f'{output_prefix}_mosaic.jpg'
        cv2.imwrite(ortho_file, orthophoto)
        print(f"正射影像已保存: {ortho_file}")
        
        # 2. 保存标签正射影像
        label_file = f'{output_prefix}_labels.png'
        cv2.imwrite(label_file, label_orthophoto * 255)
        print(f"标签正射影像已保存: {label_file}")
        
        # 3. 保存地理信息
        geo_info = {
            'bounds': self.orthophoto_bounds,
            'resolution': self.orthophoto_resolution,
            'width': orthophoto.shape[1],
            'height': orthophoto.shape[0],
            'crs': 'EPSG:4326'
        }
        
        geo_file = f'{output_prefix}_geo_info.json'
        with open(geo_file, 'w', encoding='utf-8') as f:
            json.dump(geo_info, f, indent=2)
        print(f"地理信息已保存: {geo_file}")
        
        # 4. 保存投票统计
        voting_stats = self.analyze_voting_results(pixel_votes, label_orthophoto)
        stats_file = f'{output_prefix}_voting_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(voting_stats, f, indent=2, ensure_ascii=False)
        print(f"投票统计已保存: {stats_file}")
        
        return voting_stats
    
    def analyze_voting_results(self, pixel_votes: Dict, label_orthophoto: np.ndarray) -> Dict:
        """分析投票结果"""
        total_pixels = label_orthophoto.size
        annotated_pixels = np.sum(label_orthophoto > 0)
        background_pixels = total_pixels - annotated_pixels
        
        conflict_pixels = len([v for v in pixel_votes.values() if v['total_votes'] > 1])
        
        stats = {
            'total_pixels': int(total_pixels),
            'annotated_pixels': int(annotated_pixels),
            'background_pixels': int(background_pixels),
            'annotated_percentage': float(annotated_pixels / total_pixels * 100),
            'voted_pixels': len(pixel_votes),
            'conflict_pixels': conflict_pixels,
            'processed_images': len(self.images_metadata)
        }
        
        return stats
    
    def run_complete_workflow(self, resolution: float = 0.0001, 
                            voting_threshold: float = 0.5,
                            generate_mosaic: bool = True):
        """运行完整的正射影像生成工作流程"""
        print("开始完整的正射影像生成工作流程...")
        
        # 1. 加载数据
        print("1. 加载图像元数据和标注数据...")
        self.load_image_metadata()
        
        # 2. 计算正射影像边界
        print("2. 计算正射影像边界...")
        self.calculate_orthophoto_bounds()
        
        # 3. 生成正射影像镶嵌（可选）
        orthophoto = None
        if generate_mosaic:
            print("3. 生成正射影像镶嵌...")
            orthophoto = self.generate_orthophoto_mosaic(resolution)
        else:
            print("3. 跳过正射影像镶嵌生成...")
            # 创建空白图像作为占位符
            min_lon, min_lat, max_lon, max_lat = self.orthophoto_bounds
            ortho_width = int((max_lon - min_lon) / resolution) + 1
            ortho_height = int((max_lat - min_lat) / resolution) + 1
            orthophoto = np.zeros((ortho_height, ortho_width, 3), dtype=np.uint8)
        
        # 4. 生成标签正射影像
        print("4. 生成标签正射影像...")
        label_orthophoto, pixel_votes = self.generate_label_orthophoto(
            resolution, voting_threshold
        )
        
        # 5. 保存结果
        print("5. 保存结果...")
        voting_stats = self.save_results(orthophoto, label_orthophoto, pixel_votes)
        
        # 6. 打印摘要
        self.print_workflow_summary(voting_stats)
        
        return orthophoto, label_orthophoto, voting_stats
    
    def print_workflow_summary(self, voting_stats: Dict):
        """打印工作流程摘要"""
        print("\n" + "="*60)
        print(" 正射影像生成工作流程完成")
        print("="*60)
        
        print(f"\n📊 处理统计:")
        print(f"  处理图像数: {voting_stats['processed_images']}")
        print(f"  正射影像总像素: {voting_stats['total_pixels']:,}")
        print(f"  参与投票像素: {voting_stats['voted_pixels']:,}")
        print(f"  冲突像素数: {voting_stats['conflict_pixels']:,}")
        
        print(f"\n🏠 标注结果:")
        print(f"  房屋标注像素: {voting_stats['annotated_pixels']:,}")
        print(f"  背景像素: {voting_stats['background_pixels']:,}")
        print(f"  标注覆盖率: {voting_stats['annotated_percentage']:.2f}%")

if __name__ == "__main__":
    generator = OrthophotoGenerator('.', 'label.json')
    generator.run_complete_workflow(
        resolution=0.0001,
        voting_threshold=0.5,
        generate_mosaic=False  # 设为True可生成完整正射影像镶嵌
    )
