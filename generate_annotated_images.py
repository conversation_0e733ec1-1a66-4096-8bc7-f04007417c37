#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DJI影像标注可视化系统
为每张原始图像生成带标注框的可视化结果
"""

import json
import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import urllib.parse
from datetime import datetime

class AnnotatedImageGenerator:
    def __init__(self):
        self.annotations = {}
        self.label_colors = {
            'False Positive-Confusing (1)': (255, 0, 0),      # 红色
            'Deciduous Vegetation (2)': (0, 255, 0),          # 绿色
            'Building': (0, 0, 255),                           # 蓝色
            'Road': (255, 255, 0),                             # 黄色
            'Water': (0, 255, 255),                            # 青色
            'Vegetation': (255, 0, 255)                        # 紫色
        }
        
    def load_annotations(self):
        """加载标注数据"""
        print("🏷️ 正在加载标注数据...")
        
        try:
            with open('label.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for item in data:
                # 提取图像文件名
                image_path = item['data']['image']
                raw_filename = os.path.basename(image_path)
                
                # 从URL编码的路径中提取真实文件名
                decoded_path = urllib.parse.unquote(raw_filename)
                
                # 进一步提取文件名
                if '\\' in decoded_path:
                    filename = decoded_path.split('\\')[-1]
                elif '/' in decoded_path:
                    filename = decoded_path.split('/')[-1]
                else:
                    filename = decoded_path
                
                print(f"🔍 处理文件: {raw_filename} -> {filename}")
                
                # 提取标注
                annotations = []
                if 'annotations' in item and item['annotations']:
                    for annotation in item['annotations']:
                        if 'result' in annotation:
                            for result in annotation['result']:
                                if result['type'] == 'polygonlabels':
                                    points = result['value']['points']
                                    labels = result['value']['polygonlabels']
                                    
                                    # 存储相对坐标（百分比）
                                    annotations.append({
                                        'type': 'polygon',
                                        'points': points,  # 保持百分比坐标
                                        'label': labels[0] if labels else 'Unknown'
                                    })
                                    
                                    print(f"   ✅ 添加标注: {labels[0] if labels else 'Unknown'}, {len(points)} 个点")
                
                if annotations:
                    self.annotations[filename] = annotations
                    print(f"📝 {filename}: {len(annotations)} 个标注")
                else:
                    print(f"⚠️ {filename}: 没有找到标注")
            
            print(f"📋 成功加载 {len(self.annotations)} 张图像的标注数据")
            
        except Exception as e:
            print(f"❌ 加载标注数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def draw_annotations_on_image(self, image_path, annotations):
        """在图像上绘制标注"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None
        
        height, width = image.shape[:2]
        
        # 创建绘制副本
        annotated_image = image.copy()
        
        for annotation in annotations:
            if annotation['type'] == 'polygon':
                # 转换百分比坐标为绝对坐标
                points = []
                for point in annotation['points']:
                    x = int((point[0] / 100.0) * width)
                    y = int((point[1] / 100.0) * height)
                    points.append([x, y])
                
                # 获取标签颜色
                label = annotation['label']
                color = self.label_colors.get(label, (128, 128, 128))
                
                # 绘制多边形
                if len(points) >= 3:
                    poly_array = np.array(points, dtype=np.int32)
                    
                    # 绘制填充多边形（半透明）
                    overlay = annotated_image.copy()
                    cv2.fillPoly(overlay, [poly_array], color)
                    cv2.addWeighted(annotated_image, 0.7, overlay, 0.3, 0, annotated_image)
                    
                    # 绘制边框
                    cv2.polylines(annotated_image, [poly_array], True, color, 3)
                    
                    # 计算标签位置（多边形中心）
                    M = cv2.moments(poly_array)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        # 绘制标签文本
                        font = cv2.FONT_HERSHEY_SIMPLEX
                        font_scale = 0.8
                        thickness = 2
                        
                        # 获取文本尺寸
                        (text_width, text_height), _ = cv2.getTextSize(label, font, font_scale, thickness)
                        
                        # 绘制文本背景
                        cv2.rectangle(annotated_image, 
                                    (cx - text_width//2 - 5, cy - text_height//2 - 5),
                                    (cx + text_width//2 + 5, cy + text_height//2 + 5),
                                    (0, 0, 0), -1)
                        
                        # 绘制文本
                        cv2.putText(annotated_image, label, 
                                  (cx - text_width//2, cy + text_height//2),
                                  font, font_scale, (255, 255, 255), thickness)
        
        return annotated_image
    
    def generate_all_annotated_images(self):
        """为所有图像生成标注可视化"""
        print("🎨 正在为所有图像生成标注可视化...")
        
        # 创建输出目录
        output_dir = "annotated_images"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        generated_count = 0
        
        # 遍历所有DJI图像
        for filename in os.listdir('.'):
            if filename.startswith('DJI_') and filename.endswith('.JPG'):
                print(f"🖼️ 处理图像: {filename}")
                
                # 检查是否有标注
                if filename in self.annotations:
                    annotations = self.annotations[filename]
                    
                    # 生成标注图像
                    annotated_image = self.draw_annotations_on_image(filename, annotations)
                    
                    if annotated_image is not None:
                        # 保存标注图像
                        output_path = os.path.join(output_dir, f"annotated_{filename}")
                        success = cv2.imwrite(output_path, annotated_image)
                        
                        if success:
                            print(f"   ✅ 保存成功: {output_path}")
                            generated_count += 1
                        else:
                            print(f"   ❌ 保存失败: {output_path}")
                    else:
                        print(f"   ❌ 图像处理失败")
                else:
                    print(f"   ⚠️ 没有标注数据，跳过")
        
        print(f"🎯 成功生成 {generated_count} 张标注图像")
        return generated_count
    
    def create_summary_visualization(self):
        """创建汇总可视化"""
        print("📊 正在创建汇总可视化...")
        
        # 统计信息
        total_images = len([f for f in os.listdir('.') if f.startswith('DJI_') and f.endswith('.JPG')])
        annotated_images = len(self.annotations)
        total_annotations = sum(len(annotations) for annotations in self.annotations.values())
        
        # 标签统计
        label_counts = {}
        for annotations in self.annotations.values():
            for annotation in annotations:
                label = annotation['label']
                label_counts[label] = label_counts.get(label, 0) + 1
        
        # 创建统计图表
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 图1：标注数量统计
        labels = list(label_counts.keys())
        counts = list(label_counts.values())
        colors = [np.array(self.label_colors.get(label, (128, 128, 128))) / 255.0 for label in labels]
        
        bars = ax1.bar(range(len(labels)), counts, color=colors)
        ax1.set_title('标注类别数量统计', fontsize=14, fontweight='bold')
        ax1.set_xlabel('标注类别')
        ax1.set_ylabel('数量')
        ax1.set_xticks(range(len(labels)))
        ax1.set_xticklabels(labels, rotation=45, ha='right')
        
        # 添加数值标签
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{count}', ha='center', va='bottom')
        
        # 图2：处理统计
        categories = ['总图像数', '有标注图像', '总标注数']
        values = [total_images, annotated_images, total_annotations]
        colors2 = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        bars2 = ax2.bar(categories, values, color=colors2)
        ax2.set_title('图像处理统计', fontsize=14, fontweight='bold')
        ax2.set_ylabel('数量')
        
        # 添加数值标签
        for bar, value in zip(bars2, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{value}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('annotation_summary.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 汇总可视化完成！")
        
        # 保存统计信息
        summary_info = {
            'system_info': {
                'name': 'DJI影像标注可视化系统',
                'version': '1.0',
                'description': '为每张原始图像生成带标注框的可视化结果',
                'timestamp': datetime.now().isoformat()
            },
            'statistics': {
                'total_images': total_images,
                'annotated_images': annotated_images,
                'total_annotations': total_annotations,
                'annotation_coverage_percent': (annotated_images / total_images) * 100 if total_images > 0 else 0
            },
            'label_counts': label_counts,
            'label_colors': {k: list(v) for k, v in self.label_colors.items()},
            'output_directory': 'annotated_images'
        }
        
        with open('annotation_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary_info, f, indent=2, ensure_ascii=False)
        
        return summary_info
    
    def run_complete_system(self):
        """运行完整的标注可视化系统"""
        print("=" * 70)
        print("🎨 DJI影像标注可视化系统")
        print("=" * 70)
        
        # 1. 加载标注数据
        self.load_annotations()
        
        if not self.annotations:
            print("❌ 没有找到标注数据")
            return
        
        # 2. 生成所有标注图像
        generated_count = self.generate_all_annotated_images()
        
        # 3. 创建汇总可视化
        summary_info = self.create_summary_visualization()
        
        # 4. 输出结果摘要
        print("\n" + "=" * 70)
        print("🎯 标注可视化生成完成！")
        print("=" * 70)
        print(f"📸 总图像数: {summary_info['statistics']['total_images']} 张")
        print(f"🏷️ 有标注图像: {summary_info['statistics']['annotated_images']} 张")
        print(f"📝 总标注数: {summary_info['statistics']['total_annotations']} 个")
        print(f"📊 标注覆盖率: {summary_info['statistics']['annotation_coverage_percent']:.1f}%")
        print(f"🎨 生成标注图像: {generated_count} 张")
        
        print(f"\n🏷️ 标注类别统计:")
        for label, count in summary_info['label_counts'].items():
            color = self.label_colors.get(label, (128, 128, 128))
            print(f"   • {label}: {count} 个 (颜色: RGB{color})")
        
        print(f"\n📁 输出文件:")
        print(f"   • annotated_images/ - 标注图像目录")
        print(f"   • annotation_summary.png - 统计图表")
        print(f"   • annotation_summary.json - 详细统计信息")
        
        print("\n✅ 所有原始图像的标注可视化已完成！")
        print("🖼️ 每张图像都显示了对应的标注框和标签！")

def main():
    """主函数"""
    generator = AnnotatedImageGenerator()
    generator.run_complete_system()

if __name__ == "__main__":
    main()
