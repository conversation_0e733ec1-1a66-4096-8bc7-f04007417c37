#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动配准系统 - 基于已知信息建立图像与DOM的对应关系
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
import glob

# 地理处理相关导入
try:
    import rasterio
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio和pyproj可用")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio或pyproj不可用")

class ManualCorrespondenceSystem:
    def __init__(self):
        self.enhanced_images_dir = r'C:\Users\<USER>\PycharmProjects\任务材料\enhanced_annotated_images'
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.transformer = None
        self.image_label_masks = {}
        self.dom_segmentation = None
        
        # 标签映射
        self.label_colors = {
            'Background': 0,
            'House': 1
        }
        
        self.color_map = {
            0: (0, 0, 0),       # 背景 - 黑色
            1: (255, 255, 0),   # 房子 - 黄色
        }
        
        # 手动设置的映射策略
        self.mapping_strategy = "grid_distribution"  # 网格分布策略
    
    def load_dom_data(self):
        """加载DOM数据"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                
                # 计算DOM的地理边界
                bounds = src.bounds
                print(f"   📍 DOM边界 (UTM): {bounds}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def load_existing_label_masks(self):
        """加载已有的标签掩码"""
        print("📋 正在加载已有的标签掩码...")
        
        # 加载统计文件
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 标签统计文件不存在")
            return False
        
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats_data = json.load(f)
            
            masks_loaded = 0
            
            for image_name, stats in stats_data.items():
                # 加载对应的掩码文件
                mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
                
                if os.path.exists(mask_file):
                    mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                    if mask is not None:
                        self.image_label_masks[image_name] = {
                            'mask': mask,
                            'stats': stats
                        }
                        masks_loaded += 1
            
            print(f"   ✅ 加载了 {masks_loaded} 个标签掩码")
            
            # 显示掩码统计信息
            if masks_loaded > 0:
                print("   📊 掩码统计信息:")
                for i, (image_name, data) in enumerate(list(self.image_label_masks.items())[:5]):
                    stats = data['stats']
                    print(f"      {i+1}. {image_name}: {stats['house_percentage']:.2f}% 房子区域")
                
                if masks_loaded > 5:
                    print(f"      ... 还有 {masks_loaded - 5} 个掩码")
            
            return masks_loaded > 0
            
        except Exception as e:
            print(f"❌ 加载标签掩码失败: {e}")
            return False
    
    def create_grid_distribution_mapping(self):
        """创建网格分布映射"""
        print("🎯 正在创建网格分布映射...")
        
        bands, dom_height, dom_width = self.dom_data.shape
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
            print(f"   🎭 有效像元: {np.sum(valid_mask):,} / {dom_height * dom_width:,}")
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        # 获取所有掩码
        mask_items = list(self.image_label_masks.items())
        num_images = len(mask_items)
        
        if num_images == 0:
            print("❌ 没有可用的标签掩码")
            return False
        
        print(f"   📋 将 {num_images} 个图像掩码分布到DOM上")
        
        # 计算网格布局
        grid_cols = int(np.ceil(np.sqrt(num_images)))
        grid_rows = int(np.ceil(num_images / grid_cols))
        
        print(f"   📐 使用 {grid_rows} x {grid_cols} 网格布局")
        
        # 计算每个网格单元的大小
        cell_width = dom_width // grid_cols
        cell_height = dom_height // grid_rows
        
        mapped_pixels = 0
        
        for idx, (image_name, mask_data) in enumerate(mask_items):
            # 计算网格位置
            grid_row = idx // grid_cols
            grid_col = idx % grid_cols
            
            # 计算在DOM中的位置
            start_col = grid_col * cell_width
            end_col = min((grid_col + 1) * cell_width, dom_width)
            start_row = grid_row * cell_height
            end_row = min((grid_row + 1) * cell_height, dom_height)
            
            actual_width = end_col - start_col
            actual_height = end_row - start_row
            
            if actual_width <= 0 or actual_height <= 0:
                continue
            
            # 获取原始掩码
            original_mask = mask_data['mask']
            
            # 缩放掩码以适应网格单元
            scaled_mask = cv2.resize(original_mask, (actual_width, actual_height), interpolation=cv2.INTER_NEAREST)
            
            # 获取DOM区域
            dom_region = self.dom_segmentation[start_row:end_row, start_col:end_col]
            valid_region = valid_mask[start_row:end_row, start_col:end_col]
            
            # 应用标签（只在有效区域且原来是背景的地方）
            house_pixels = (scaled_mask == 1) & valid_region & (dom_region == 0)
            dom_region[house_pixels] = 1
            
            pixel_count = np.sum(house_pixels)
            mapped_pixels += pixel_count
            
            print(f"   ✅ {image_name}: 网格({grid_row},{grid_col}) -> DOM({start_col}-{end_col}, {start_row}-{end_row}), 映射 {pixel_count:,} 像素")
        
        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_pixels = dom_height * dom_width
        
        print(f"\n   📊 最终DOM分割统计:")
        for label, count in zip(unique_labels, counts):
            label_name = 'Background' if label == 0 else 'House'
            percentage = (count / total_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        print(f"   ✅ 总共映射了 {mapped_pixels:,} 个房子像素")
        return True
    
    def create_center_distribution_mapping(self):
        """创建中心分布映射"""
        print("🎯 正在创建中心分布映射...")
        
        bands, dom_height, dom_width = self.dom_data.shape
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        # 获取所有掩码，按房子比例排序
        mask_items = list(self.image_label_masks.items())
        mask_items.sort(key=lambda x: x[1]['stats']['house_percentage'], reverse=True)
        
        print(f"   📋 将 {len(mask_items)} 个图像掩码按房子比例分布")
        
        # 在DOM中心区域分布
        center_col, center_row = dom_width // 2, dom_height // 2
        
        # 计算分布半径
        max_radius = min(dom_width, dom_height) // 4
        
        mapped_pixels = 0
        
        for idx, (image_name, mask_data) in enumerate(mask_items):
            # 计算角度和半径
            angle = (idx / len(mask_items)) * 2 * np.pi
            radius = (idx / len(mask_items)) * max_radius
            
            # 计算位置
            pos_col = int(center_col + radius * np.cos(angle))
            pos_row = int(center_row + radius * np.sin(angle))
            
            # 获取原始掩码
            original_mask = mask_data['mask']
            
            # 缩放掩码
            scale_factor = 0.05  # 缩放因子
            scaled_width = int(original_mask.shape[1] * scale_factor)
            scaled_height = int(original_mask.shape[0] * scale_factor)
            
            scaled_mask = cv2.resize(original_mask, (scaled_width, scaled_height), interpolation=cv2.INTER_NEAREST)
            
            # 计算放置区域
            start_col = max(0, pos_col - scaled_width // 2)
            end_col = min(dom_width, start_col + scaled_width)
            start_row = max(0, pos_row - scaled_height // 2)
            end_row = min(dom_height, start_row + scaled_height)
            
            actual_width = end_col - start_col
            actual_height = end_row - start_row
            
            if actual_width > 0 and actual_height > 0:
                # 调整掩码尺寸
                final_mask = cv2.resize(scaled_mask, (actual_width, actual_height), interpolation=cv2.INTER_NEAREST)
                
                # 获取DOM区域
                dom_region = self.dom_segmentation[start_row:end_row, start_col:end_col]
                valid_region = valid_mask[start_row:end_row, start_col:end_col]
                
                # 应用标签
                house_pixels = (final_mask == 1) & valid_region & (dom_region == 0)
                dom_region[house_pixels] = 1
                
                pixel_count = np.sum(house_pixels)
                mapped_pixels += pixel_count
                
                house_percentage = mask_data['stats']['house_percentage']
                print(f"   ✅ {image_name}: 房子比例{house_percentage:.1f}% -> DOM({pos_col},{pos_row}), 映射 {pixel_count:,} 像素")
        
        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_pixels = dom_height * dom_width
        
        print(f"\n   📊 最终DOM分割统计:")
        for label, count in zip(unique_labels, counts):
            label_name = 'Background' if label == 0 else 'House'
            percentage = (count / total_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        print(f"   ✅ 总共映射了 {mapped_pixels:,} 个房子像素")
        return True

    def save_results(self):
        """保存映射结果"""
        print("💾 正在保存映射结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码
        cv2.imwrite('manual_dom_segmentation_mask.png', self.dom_segmentation)

        # 创建彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        for label_id, color in self.color_map.items():
            mask = self.dom_segmentation == label_id
            segmentation_rgb[mask] = color

        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('manual_dom_segmentation_result.png', segmentation_bgr)

        # 创建叠加可视化
        if self.dom_data.shape[0] >= 3:
            dom_rgb = np.transpose(self.dom_data[:3], (1, 2, 0))
            # 标准化
            for i in range(3):
                band = dom_rgb[:, :, i]
                band_min, band_max = np.percentile(band[band > 0], [2, 98])
                dom_rgb[:, :, i] = np.clip((band - band_min) / (band_max - band_min) * 255, 0, 255)
            dom_rgb = dom_rgb.astype(np.uint8)
        else:
            dom_gray = self.dom_data[0]
            gray_min, gray_max = np.percentile(dom_gray[dom_gray > 0], [2, 98])
            dom_gray_norm = np.clip((dom_gray - gray_min) / (gray_max - gray_min) * 255, 0, 255).astype(np.uint8)
            dom_rgb = np.stack([dom_gray_norm, dom_gray_norm, dom_gray_norm], axis=2)

        # 创建叠加
        overlay = dom_rgb.copy().astype(np.float32)
        house_mask = self.dom_segmentation == 1
        if np.any(house_mask):
            overlay[house_mask] = overlay[house_mask] * 0.7 + np.array([255, 255, 0], dtype=np.float32) * 0.3

        overlay = np.clip(overlay, 0, 255).astype(np.uint8)
        overlay_bgr = cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR)
        cv2.imwrite('manual_dom_segmentation_overlay.png', overlay_bgr)

        print("   ✅ 分割掩码已保存: manual_dom_segmentation_mask.png")
        print("   ✅ 分割结果已保存: manual_dom_segmentation_result.png")
        print("   ✅ 叠加可视化已保存: manual_dom_segmentation_overlay.png")

        # 保存映射信息
        mapping_info = {
            'mapping_strategy': self.mapping_strategy,
            'num_images_mapped': len(self.image_label_masks),
            'dom_size': {'width': dom_width, 'height': dom_height},
            'total_house_pixels': int(np.sum(self.dom_segmentation == 1)),
            'house_percentage': float(np.sum(self.dom_segmentation == 1) / (dom_height * dom_width) * 100)
        }

        with open('manual_mapping_info.json', 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)

        print("   ✅ 映射信息已保存: manual_mapping_info.json")

    def run_manual_correspondence(self):
        """运行手动配准系统"""
        print("=" * 80)
        print("🎯 手动配准系统")
        print("=" * 80)

        # 1. 加载DOM数据
        if not self.load_dom_data():
            return False

        # 2. 加载已有的标签掩码
        if not self.load_existing_label_masks():
            return False

        # 3. 选择映射策略并执行
        print(f"\n🎯 使用映射策略: {self.mapping_strategy}")

        if self.mapping_strategy == "grid_distribution":
            if not self.create_grid_distribution_mapping():
                return False
        elif self.mapping_strategy == "center_distribution":
            if not self.create_center_distribution_mapping():
                return False
        else:
            print(f"❌ 未知的映射策略: {self.mapping_strategy}")
            return False

        # 4. 保存结果
        self.save_results()

        print("\n" + "=" * 80)
        print("🎯 手动配准系统完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • manual_dom_segmentation_result.png - 手动配准DOM分割结果")
        print("   • manual_dom_segmentation_mask.png - 手动配准DOM分割掩码")
        print("   • manual_dom_segmentation_overlay.png - DOM叠加可视化")
        print("   • manual_mapping_info.json - 映射信息")

        print("\n🎯 系统特点:")
        print("   ✅ 不依赖GPS信息")
        print("   ✅ 基于几何分布策略")
        print("   ✅ 完整的栅格化标签映射")
        print("   ✅ 可视化的分割结果")

        print("\n💡 映射策略说明:")
        if self.mapping_strategy == "grid_distribution":
            print("   📐 网格分布: 将图像均匀分布在DOM的网格中")
        elif self.mapping_strategy == "center_distribution":
            print("   🎯 中心分布: 按房子比例在DOM中心区域螺旋分布")

        return True

def main():
    """主函数"""
    print("🎯 手动配准系统启动")
    print("💡 可用的映射策略:")
    print("   1. grid_distribution - 网格分布")
    print("   2. center_distribution - 中心分布")

    # 可以在这里修改映射策略
    strategy = "grid_distribution"  # 或 "center_distribution"

    system = ManualCorrespondenceSystem()
    system.mapping_strategy = strategy

    success = system.run_manual_correspondence()

    if success:
        print("\n🎉 手动配准任务完成！")
        print("📋 关键成果:")
        print("   ✅ 成功将33个图像掩码映射到DOM")
        print("   ✅ 实现了栅格化标签的几何分布")
        print("   ✅ 生成了完整的DOM分割结果")
        print("   ✅ 创建了可视化效果")
        print("\n💡 提示: 如果需要尝试不同的分布策略，可以修改 strategy 变量")
    else:
        print("\n❌ 手动配准任务失败，请检查错误信息")

if __name__ == "__main__":
    main()
