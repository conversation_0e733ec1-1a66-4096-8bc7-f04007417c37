#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实地理映射系统 - 建立原始影像与DOM的真实地理对应关系
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import glob

# 地理处理相关导入
try:
    import rasterio
    from pyproj import Transformer
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio可用")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("❌ Rasterio不可用")

class TrueGeographicMapping:
    def __init__(self):
        self.enhanced_images_dir = r'C:\Users\<USER>\PycharmProjects\任务材料\enhanced_annotated_images'
        self.original_images_dir = None  # 需要找到原始图像目录
        self.dom_data = None
        self.dom_transform = None
        self.dom_crs = None
        self.transformer = None
        self.image_gps_data = {}
        self.image_label_masks = {}
        self.dom_segmentation = None
        
    def find_original_images(self):
        """查找原始图像目录"""
        print("🔍 查找原始图像目录...")
        
        # 可能的原始图像目录
        possible_dirs = [
            r'C:\Users\<USER>\PycharmProjects\任务材料\images',
            r'C:\Users\<USER>\PycharmProjects\任务材料\original_images',
            r'C:\Users\<USER>\PycharmProjects\任务材料\DJI_images',
            r'C:\Users\<USER>\PycharmProjects\任务材料',
        ]
        
        for dir_path in possible_dirs:
            if os.path.exists(dir_path):
                # 查找DJI图像文件
                pattern = os.path.join(dir_path, 'DJI_*.JPG')
                files = glob.glob(pattern)
                if files:
                    self.original_images_dir = dir_path
                    print(f"   ✅ 找到原始图像目录: {dir_path}")
                    print(f"   📁 包含 {len(files)} 个DJI图像文件")
                    return True
        
        print("   ❌ 未找到原始图像目录")
        print("   💡 请确认原始DJI图像文件的位置")
        return False
    
    def extract_gps_from_original_images(self):
        """从原始图像提取GPS信息"""
        print("📍 从原始图像提取GPS信息...")
        
        if not self.original_images_dir:
            if not self.find_original_images():
                return False
        
        # 查找所有原始DJI图像
        pattern = os.path.join(self.original_images_dir, 'DJI_*.JPG')
        original_files = glob.glob(pattern)
        
        print(f"   📁 找到 {len(original_files)} 个原始图像文件")
        
        gps_extracted = 0
        
        for image_path in original_files:
            filename = os.path.basename(image_path)
            print(f"   🔍 处理: {filename}")
            
            gps_data = self.extract_gps_from_exif(image_path)
            
            if gps_data:
                self.image_gps_data[filename] = gps_data
                gps_extracted += 1
                print(f"      ✅ GPS: {gps_data['latitude']:.6f}, {gps_data['longitude']:.6f}")
            else:
                print(f"      ❌ 无GPS信息")
        
        print(f"   📊 成功提取 {gps_extracted}/{len(original_files)} 个图像的GPS数据")
        return gps_extracted > 0
    
    def extract_gps_from_exif(self, image_path):
        """从图像EXIF中提取GPS信息"""
        try:
            with Image.open(image_path) as img:
                exif_data = img._getexif()
                
                if exif_data is None:
                    return None
                
                gps_info = {}
                for tag, value in exif_data.items():
                    tag_name = TAGS.get(tag, tag)
                    if tag_name == 'GPSInfo':
                        for gps_tag, gps_value in value.items():
                            gps_tag_name = GPSTAGS.get(gps_tag, gps_tag)
                            gps_info[gps_tag_name] = gps_value
                
                if not gps_info:
                    return None
                
                # 转换GPS坐标
                lat = self.convert_gps_coordinate(gps_info.get('GPSLatitude'), gps_info.get('GPSLatitudeRef'))
                lon = self.convert_gps_coordinate(gps_info.get('GPSLongitude'), gps_info.get('GPSLongitudeRef'))
                alt = gps_info.get('GPSAltitude', 0)
                
                if lat is not None and lon is not None:
                    return {
                        'latitude': lat,
                        'longitude': lon,
                        'altitude': alt,
                        'raw_gps_info': gps_info
                    }
                
        except Exception as e:
            print(f"   ⚠️ 提取GPS信息失败 {image_path}: {e}")
        
        return None
    
    def convert_gps_coordinate(self, coord_tuple, ref):
        """转换GPS坐标格式"""
        if coord_tuple is None or ref is None:
            return None
        
        try:
            degrees, minutes, seconds = coord_tuple
            decimal_degrees = float(degrees) + float(minutes)/60 + float(seconds)/3600
            
            if ref in ['S', 'W']:
                decimal_degrees = -decimal_degrees
            
            return decimal_degrees
        except:
            return None
    
    def load_dom_data(self):
        """加载DOM数据"""
        print("🗺️ 正在加载DOM数据...")
        
        dom_file = os.path.join('dom', '0708_transparent_mosaic_group1.tif')
        if not os.path.exists(dom_file):
            print("❌ DOM文件不存在")
            return False
        
        try:
            with rasterio.open(dom_file) as src:
                self.dom_data = src.read()
                self.dom_transform = src.transform
                self.dom_crs = src.crs
                
                print(f"   ✅ DOM尺寸: {src.width} x {src.height}")
                print(f"   ✅ DOM坐标系: {self.dom_crs}")
                
                # 创建坐标转换器
                self.transformer = Transformer.from_crs('EPSG:4326', self.dom_crs, always_xy=True)
                
                # 计算DOM的地理边界
                bounds = src.bounds
                print(f"   📍 DOM边界 (UTM): {bounds}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载DOM失败: {e}")
            return False
    
    def load_existing_label_masks(self):
        """加载已有的标签掩码"""
        print("📋 正在加载已有的标签掩码...")
        
        # 加载统计文件
        stats_file = 'fixed_enhanced_label_mapping_stats.json'
        if not os.path.exists(stats_file):
            print("❌ 标签统计文件不存在")
            return False
        
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats_data = json.load(f)
            
            masks_loaded = 0
            
            for image_name, stats in stats_data.items():
                # 加载对应的掩码文件
                mask_file = f"mask_{image_name.replace('.JPG', '.png')}"
                
                if os.path.exists(mask_file):
                    mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
                    if mask is not None:
                        self.image_label_masks[image_name] = {
                            'mask': mask,
                            'stats': stats
                        }
                        masks_loaded += 1
            
            print(f"   ✅ 加载了 {masks_loaded} 个标签掩码")
            return masks_loaded > 0
            
        except Exception as e:
            print(f"❌ 加载标签掩码失败: {e}")
            return False
    
    def map_images_to_dom_with_gps(self):
        """使用GPS信息将图像映射到DOM"""
        print("🎯 使用GPS信息进行真实地理映射...")
        
        if not self.image_gps_data:
            print("❌ 没有GPS数据，无法进行真实映射")
            return False
        
        bands, dom_height, dom_width = self.dom_data.shape
        self.dom_segmentation = np.zeros((dom_height, dom_width), dtype=np.uint8)
        
        # 检查Alpha通道
        has_alpha = bands >= 4
        if has_alpha:
            alpha_channel = self.dom_data[3]
            valid_mask = alpha_channel > 0
        else:
            valid_mask = np.ones((dom_height, dom_width), dtype=bool)
        
        mapped_images = 0
        total_mapped_pixels = 0
        
        for filename, gps_data in self.image_gps_data.items():
            # 检查是否有对应的标签掩码
            if filename not in self.image_label_masks:
                print(f"   ⚠️ 未找到标签掩码: {filename}")
                continue
            
            lat, lon = gps_data['latitude'], gps_data['longitude']
            
            # 转换为DOM坐标系 (UTM)
            try:
                utm_x, utm_y = self.transformer.transform(lon, lat)
                
                # 转换为DOM像素坐标
                col, row = ~self.dom_transform * (utm_x, utm_y)
                col, row = int(col), int(row)
                
                # 检查是否在DOM范围内
                if 0 <= col < dom_width and 0 <= row < dom_height:
                    # 获取标签掩码
                    mask_data = self.image_label_masks[filename]
                    label_mask = mask_data['mask']
                    
                    print(f"   🎯 映射 {filename} 到 DOM({col}, {row})")
                    
                    # 这里需要实现真实的几何变换
                    # 简化版本：在GPS位置周围放置标签
                    mapped_pixels = self.place_labels_at_gps_location(
                        label_mask, col, row, dom_height, dom_width, valid_mask
                    )
                    
                    total_mapped_pixels += mapped_pixels
                    mapped_images += 1
                    
                    print(f"      ✅ 映射了 {mapped_pixels:,} 个房子像素")
                else:
                    print(f"   ⚠️ {filename}: GPS位置超出DOM范围 ({col}, {row})")
                
            except Exception as e:
                print(f"   ❌ {filename}: 坐标转换失败 - {e}")
        
        print(f"   📊 成功映射 {mapped_images} 个图像，总计 {total_mapped_pixels:,} 个房子像素")
        
        # 统计最终结果
        unique_labels, counts = np.unique(self.dom_segmentation, return_counts=True)
        total_pixels = dom_height * dom_width
        
        print(f"   📊 最终DOM分割统计:")
        for label, count in zip(unique_labels, counts):
            label_name = 'Background' if label == 0 else 'House'
            percentage = (count / total_pixels) * 100
            print(f"      {label_name}: {count:,} 像元 ({percentage:.2f}%)")
        
        return mapped_images > 0
    
    def place_labels_at_gps_location(self, label_mask, center_col, center_row, dom_height, dom_width, valid_mask):
        """在GPS位置放置标签"""
        # 简化版本：假设每个图像覆盖DOM中的一个固定大小区域
        # 实际应用中需要考虑图像的实际地面覆盖范围
        
        img_height, img_width = label_mask.shape
        
        # 假设图像覆盖100x100米的区域（需要根据实际情况调整）
        # DOM的分辨率可以从transform中获取
        pixel_size = abs(self.dom_transform[0])  # 每像素的米数
        coverage_meters = 100  # 假设每张图像覆盖100米
        coverage_pixels = int(coverage_meters / pixel_size)
        
        # 缩放标签掩码到DOM分辨率
        scaled_mask = cv2.resize(label_mask, (coverage_pixels, coverage_pixels), interpolation=cv2.INTER_NEAREST)
        
        # 计算放置位置
        start_col = center_col - coverage_pixels // 2
        end_col = start_col + coverage_pixels
        start_row = center_row - coverage_pixels // 2
        end_row = start_row + coverage_pixels
        
        # 确保在DOM范围内
        start_col = max(0, start_col)
        end_col = min(dom_width, end_col)
        start_row = max(0, start_row)
        end_row = min(dom_height, end_row)
        
        actual_width = end_col - start_col
        actual_height = end_row - start_row
        
        if actual_width > 0 and actual_height > 0:
            # 调整掩码尺寸
            final_mask = cv2.resize(scaled_mask, (actual_width, actual_height), interpolation=cv2.INTER_NEAREST)
            
            # 获取DOM区域
            dom_region = self.dom_segmentation[start_row:end_row, start_col:end_col]
            valid_region = valid_mask[start_row:end_row, start_col:end_col]
            
            # 应用标签（只在有效区域且原来是背景的地方）
            house_pixels = (final_mask == 1) & valid_region & (dom_region == 0)
            dom_region[house_pixels] = 1
            
            return np.sum(house_pixels)
        
        return 0

    def save_results(self):
        """保存真实地理映射结果"""
        print("💾 正在保存真实地理映射结果...")

        if self.dom_segmentation is None:
            print("❌ DOM分割结果不存在")
            return

        # 保存分割掩码
        cv2.imwrite('true_geographic_mapping_mask.png', self.dom_segmentation)

        # 创建彩色分割图
        dom_height, dom_width = self.dom_segmentation.shape
        segmentation_rgb = np.zeros((dom_height, dom_width, 3), dtype=np.uint8)

        # 背景黑色，房子黄色
        segmentation_rgb[self.dom_segmentation == 0] = (0, 0, 0)
        segmentation_rgb[self.dom_segmentation == 1] = (255, 255, 0)

        segmentation_bgr = cv2.cvtColor(segmentation_rgb, cv2.COLOR_RGB2BGR)
        cv2.imwrite('true_geographic_mapping_result.png', segmentation_bgr)

        print("   ✅ 分割掩码已保存: true_geographic_mapping_mask.png")
        print("   ✅ 分割结果已保存: true_geographic_mapping_result.png")

        # 保存GPS对应关系（转换为可序列化格式）
        serializable_gps_data = {}
        for filename, gps_data in self.image_gps_data.items():
            serializable_gps_data[filename] = {
                'latitude': float(gps_data['latitude']),
                'longitude': float(gps_data['longitude']),
                'altitude': float(gps_data.get('altitude', 0))
            }

        with open('true_gps_correspondence.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_gps_data, f, indent=2, ensure_ascii=False)

        print("   ✅ GPS对应关系已保存: true_gps_correspondence.json")

    def run_true_geographic_mapping(self):
        """运行真实地理映射系统"""
        print("=" * 80)
        print("🌍 真实地理映射系统")
        print("=" * 80)

        # 1. 加载DOM数据
        if not self.load_dom_data():
            return False

        # 2. 从原始图像提取GPS信息
        if not self.extract_gps_from_original_images():
            print("❌ 无法提取GPS信息，无法建立真实地理对应关系")
            print("💡 建议:")
            print("   1. 确认原始DJI图像文件位置")
            print("   2. 检查图像是否包含GPS EXIF信息")
            print("   3. 如果没有GPS信息，需要其他方法建立对应关系")
            return False

        # 3. 加载已有的标签掩码
        if not self.load_existing_label_masks():
            return False

        # 4. 使用GPS信息进行真实映射
        if not self.map_images_to_dom_with_gps():
            return False

        # 5. 保存结果
        self.save_results()

        print("\n" + "=" * 80)
        print("🌍 真实地理映射完成！")
        print("=" * 80)
        print("📁 生成的文件:")
        print("   • true_geographic_mapping_result.png - 基于GPS的真实映射结果")
        print("   • true_geographic_mapping_mask.png - 真实映射掩码")
        print("   • true_gps_correspondence.json - GPS对应关系")

        print("\n🌍 系统特点:")
        print("   ✅ 基于真实GPS坐标")
        print("   ✅ 不依赖图像特征")
        print("   ✅ 纯几何坐标变换")
        print("   ✅ 直接标签映射")

        return True

def main():
    """主函数"""
    system = TrueGeographicMapping()
    success = system.run_true_geographic_mapping()

    if success:
        print("\n🎉 真实地理映射成功！")
        print("📋 关键成果:")
        print("   ✅ 提取了原始图像的GPS信息")
        print("   ✅ 建立了真实的地理对应关系")
        print("   ✅ 实现了基于坐标的直接标签映射")
        print("   ✅ 不依赖任何图像特征")
    else:
        print("\n❌ 真实地理映射失败")
        print("💡 可能需要:")
        print("   1. 找到包含GPS信息的原始图像")
        print("   2. 或使用其他方法建立地理对应关系")

if __name__ == "__main__":
    main()
